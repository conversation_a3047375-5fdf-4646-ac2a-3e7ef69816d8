import React, { useState, useEffect } from 'react';
import { View, ScrollView, Alert, Image } from 'react-native';
import { Text, TextInput, Button, Card, ActivityIndicator, Menu, TouchableRipple, IconButton } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { projectUpdatesService } from '../../services/projectUpdatesService';
import { Project, ProjectUpdate, ProjectUpdateFormData } from '../../types/projectUpdates';

interface EditProjectUpdateScreenProps {
  navigation: any;
  route: {
    params: {
      updateId: string;
    };
  };
}

const EditProjectUpdateScreen: React.FC<EditProjectUpdateScreenProps> = ({ navigation, route }) => {
  const { updateId } = route.params;
  
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [heading, setHeading] = useState('');
  const [description, setDescription] = useState('');
  const [selectedImage, setSelectedImage] = useState<{
    uri: string;
    name: string;
    type: string;
  } | null>(null);
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [projectMenuVisible, setProjectMenuVisible] = useState(false);
  
  const currentUser = supabaseAuthService.getCurrentUser();

  useEffect(() => {
    loadData();
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Sorry, we need camera roll permissions to upload images.'
      );
    }
  };

  const loadData = async () => {
    try {
      console.log('Loading data for update ID:', updateId);

      // Load projects and the specific update
      const [projectsData, updateData] = await Promise.all([
        projectUpdatesService.getProjects(),
        projectUpdatesService.getProjectUpdateById(updateId)
      ]);

      console.log('Projects loaded:', projectsData.length);
      console.log('Update data loaded:', updateData);

      setProjects(projectsData.filter(p => p.status === 'active'));

      if (updateData) {
        console.log('Setting form data from update:', {
          heading: updateData.heading,
          description: updateData.description,
          projectId: updateData.projectId,
          imageUrl: updateData.imageUrl
        });

        setHeading(updateData.heading || '');
        setDescription(updateData.description || '');

        // Find and set the selected project
        const project = projectsData.find(p => p.id === updateData.projectId);
        console.log('Found project for update:', project);
        setSelectedProject(project || null);

        // Set existing image if available
        if (updateData.imageUrl) {
          console.log('Setting existing image:', updateData.imageUrl);
          setSelectedImage({
            uri: updateData.imageUrl,
            name: updateData.imageName || 'existing_image.jpg',
            type: 'image/jpeg'
          });
        }
      } else {
        console.warn('No update data found for ID:', updateId);
        Alert.alert('Error', 'Project update not found.');
      }
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', `Failed to load update data: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage({
          uri: asset.uri,
          name: asset.fileName || `image_${Date.now()}.jpg`,
          type: asset.type || 'image/jpeg',
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
  };

  const validateForm = (): boolean => {
    if (!selectedProject) {
      Alert.alert('Validation Error', 'Please select a project.');
      return false;
    }
    if (!heading.trim()) {
      Alert.alert('Validation Error', 'Please enter a heading.');
      return false;
    }
    if (!description.trim()) {
      Alert.alert('Validation Error', 'Please enter a description.');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setSubmitting(true);
    try {
      const formData: ProjectUpdateFormData = {
        projectId: selectedProject!.id,
        heading: heading.trim(),
        description: description.trim(),
        image: selectedImage,
      };

      const creator = {
        id: currentUser.id,
        name: currentUser.name || currentUser.email,
        role: currentUser.role as 'manager' | 'superadmin',
      };

      console.log('Updating project update with data:', formData);

      await projectUpdatesService.updateProjectUpdate(updateId, formData, creator);
      console.log('Project update updated successfully');

      Alert.alert(
        'Success',
        'Project update updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Detailed error updating project update:', error);
      Alert.alert('Error', `Failed to update project update: ${error.message}`);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f5f5f5' }}>
        <ActivityIndicator size="large" color="#1e3a8a" />
        <Text style={{ marginTop: 16, fontSize: 16, color: '#666' }}>Loading project update...</Text>
        <Text style={{ marginTop: 8, fontSize: 12, color: '#999' }}>Please wait while we fetch the data</Text>
      </View>
    );
  }

  return (
    <ScrollView style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      <View style={{ padding: 16 }}>
        <Card style={{ padding: 16, marginBottom: 16 }}>
          <Text style={{ fontSize: 20, fontWeight: 'bold', marginBottom: 16, color: '#1e3a8a' }}>
            Edit Project Update
          </Text>

          {/* Project Selection */}
          <Text style={{ fontSize: 14, fontWeight: 'bold', marginBottom: 8, color: '#333' }}>
            Project *
          </Text>
          <Menu
            visible={projectMenuVisible}
            onDismiss={() => setProjectMenuVisible(false)}
            anchor={
              <TouchableRipple
                onPress={() => setProjectMenuVisible(true)}
                style={{
                  borderWidth: 1,
                  borderColor: '#ddd',
                  borderRadius: 4,
                  padding: 12,
                  marginBottom: 16,
                  backgroundColor: '#fff'
                }}
              >
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text style={{ color: selectedProject ? '#333' : '#999' }}>
                    {selectedProject ? selectedProject.name : 'Select a project'}
                  </Text>
                  <MaterialCommunityIcons name="chevron-down" size={20} color="#666" />
                </View>
              </TouchableRipple>
            }
          >
            {projects.map((project) => (
              <Menu.Item
                key={project.id}
                onPress={() => {
                  setSelectedProject(project);
                  setProjectMenuVisible(false);
                }}
                title={project.name}
              />
            ))}
          </Menu>

          {/* Heading Input */}
          <Text style={{ fontSize: 14, fontWeight: 'bold', marginBottom: 8, color: '#333' }}>
            Heading *
          </Text>
          <TextInput
            value={heading}
            onChangeText={setHeading}
            placeholder="Enter update heading"
            style={{ marginBottom: 16, backgroundColor: '#fff' }}
            mode="outlined"
          />

          {/* Description Input */}
          <Text style={{ fontSize: 14, fontWeight: 'bold', marginBottom: 8, color: '#333' }}>
            Description *
          </Text>
          <TextInput
            value={description}
            onChangeText={setDescription}
            placeholder="Enter update description"
            multiline
            numberOfLines={4}
            style={{ marginBottom: 16, backgroundColor: '#fff' }}
            mode="outlined"
          />

          {/* Image Upload */}
          <Text style={{ fontSize: 14, fontWeight: 'bold', marginBottom: 8, color: '#333' }}>
            Image (Optional)
          </Text>
          {selectedImage ? (
            <View style={{ position: 'relative', marginBottom: 16 }}>
              <Image 
                source={{ uri: selectedImage.uri }} 
                style={{ 
                  width: '100%', 
                  height: 200, 
                  borderRadius: 8,
                  backgroundColor: '#f0f0f0'
                }} 
                resizeMode="cover"
              />
              <IconButton
                icon="close-circle"
                size={24}
                iconColor="#ef4444"
                style={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  backgroundColor: 'rgba(255,255,255,0.8)'
                }}
                onPress={removeImage}
              />
            </View>
          ) : (
            <TouchableRipple 
              onPress={pickImage} 
              style={{
                borderWidth: 2,
                borderColor: '#ddd',
                borderStyle: 'dashed',
                borderRadius: 8,
                padding: 32,
                alignItems: 'center',
                marginBottom: 16,
                backgroundColor: '#fafafa'
              }}
            >
              <View style={{ alignItems: 'center' }}>
                <MaterialCommunityIcons name="camera-plus" size={48} color="#666" />
                <Text style={{ marginTop: 8, color: '#666', fontSize: 16 }}>Tap to add image</Text>
                <Text style={{ color: '#999', fontSize: 12, marginTop: 4 }}>
                  Choose from gallery
                </Text>
              </View>
            </TouchableRipple>
          )}

          {/* Submit Button */}
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={submitting}
            disabled={submitting}
            style={{ 
              backgroundColor: '#1e3a8a',
              paddingVertical: 8,
              marginTop: 8
            }}
          >
            {submitting ? 'Updating...' : 'Update Project Update'}
          </Button>
        </Card>
      </View>
    </ScrollView>
  );
};

export default EditProjectUpdateScreen;
