import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createDrawerNavigator, DrawerContentScrollView, DrawerItem, DrawerItemList } from '@react-navigation/drawer';
import AuthStack from './AuthStack';
import { supabaseAuthService, User } from '../services/supabaseAuth';
import { ActivityIndicator } from 'react-native-paper';
import { View, Text, Image } from 'react-native';
import SplashScreen from '../screens/SplashScreen';
import EmployeeDashboard from '../screens/dashboard/EmployeeDashboard';
import SuperadminDashboard from '../screens/dashboard/SuperadminDashboard';
import ManagerDashboard from '../screens/dashboard/ManagerDashboard';
import EditProfileScreen from '../screens/dashboard/EditProfileScreen';
import ChangePasswordScreen from '../screens/dashboard/ChangePasswordScreen';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Avatar, Badge } from 'react-native-paper';
import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import type { ReactNode } from 'react';
import LeaveListScreen from '../screens/leave/LeaveListScreen';
import LeaveApplyScreen from '../screens/leave/LeaveApplyScreen';
import CustomAppbar from '../components/CustomAppbar';
import AnnouncementListScreen from '../screens/announcements/AnnouncementListScreen';
import AnnouncementAddScreen from '../screens/announcements/AnnouncementAddScreen';
import DirectoryScreen from '../screens/directory/DirectoryScreen';
import EmployeeDetailsScreen from '../screens/directory/EmployeeDetailsScreen';
import AdminPanelScreen from '../screens/admin/AdminPanelScreen';
import ProjectManagementScreen from '../screens/admin/ProjectManagementScreen';

import TeamManagementScreen from '../screens/team/TeamManagementScreen';
import ProjectUpdatesListScreen from '../screens/projectUpdates/ProjectUpdatesListScreen';
import AddProjectUpdateScreen from '../screens/projectUpdates/AddProjectUpdateScreen';
import EditProjectUpdateScreen from '../screens/projectUpdates/EditProjectUpdateScreen';

const Drawer = createDrawerNavigator();
const EmployeeStack = createNativeStackNavigator();
const SuperadminStack = createNativeStackNavigator();
const ManagerStack = createNativeStackNavigator();
const LeaveStack = createNativeStackNavigator();
const AnnouncementStack = createNativeStackNavigator();
const TeamStack = createNativeStackNavigator();
const ProjectUpdatesStack = createNativeStackNavigator();
const DirectoryStack = createNativeStackNavigator();

// Placeholder screens for drawer items
const PlaceholderScreen = ({ route }: any) => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text style={{ fontSize: 22 }}>{route.name}</Text>
  </View>
);

const EmployeeStackNavigator = () => (
  <EmployeeStack.Navigator>
    <EmployeeStack.Screen name="EmployeeDashboard" component={EmployeeDashboard} options={{ headerShown: false }} />
    <EmployeeStack.Screen name="EditProfile" component={EditProfileScreen} options={{ title: 'Edit Profile', headerShown: false }} />
    <EmployeeStack.Screen name="ChangePassword" component={ChangePasswordScreen} options={{ title: 'Change Password', headerShown: false }} />
  </EmployeeStack.Navigator>
);

const ManagerStackNavigator = () => (
  <ManagerStack.Navigator>
    <ManagerStack.Screen name="ManagerDashboard" component={ManagerDashboard} options={{ headerShown: false }} />
    <ManagerStack.Screen name="EditProfile" component={EditProfileScreen} options={{ title: 'Edit Profile', headerShown: false }} />
    <ManagerStack.Screen name="ChangePassword" component={ChangePasswordScreen} options={{ title: 'Change Password', headerShown: false }} />
  </ManagerStack.Navigator>
);

const SuperadminStackNavigator = () => (
  <SuperadminStack.Navigator>
    <SuperadminStack.Screen name="SuperadminDashboard" component={SuperadminDashboard} options={{ headerShown: false }} />
    <SuperadminStack.Screen name="EditProfile" component={EditProfileScreen} options={{ title: 'Edit Profile', headerShown: false }} />
    <SuperadminStack.Screen name="ChangePassword" component={ChangePasswordScreen} options={{ title: 'Change Password', headerShown: false }} />
  </SuperadminStack.Navigator>
);

const LeaveStackNavigator = () => (
  <LeaveStack.Navigator>
    <LeaveStack.Screen name="LeaveList" component={LeaveListScreen} options={{ title: 'Leave Management' }} />
    <LeaveStack.Screen name="LeaveApply" component={LeaveApplyScreen} options={{ title: 'Apply for Leave' }} />
  </LeaveStack.Navigator>
);

const AnnouncementStackNavigator = () => (
  <AnnouncementStack.Navigator>
    <AnnouncementStack.Screen name="AnnouncementList" component={AnnouncementListScreen} options={{ title: 'Announcements', headerShown: false }} />
    <AnnouncementStack.Screen name="AnnouncementAdd" component={AnnouncementAddScreen} options={{ title: 'Add Announcement' }} />
  </AnnouncementStack.Navigator>
);

const TeamStackNavigator = () => (
  <TeamStack.Navigator>
    <TeamStack.Screen name="TeamManagement" component={TeamManagementScreen} options={{ title: 'Team Management' }} />
  </TeamStack.Navigator>
);

const ProjectUpdatesStackNavigator = () => (
  <ProjectUpdatesStack.Navigator>
    <ProjectUpdatesStack.Screen
      name="ProjectUpdatesList"
      component={ProjectUpdatesListScreen}
      options={{ title: 'Project Updates', headerShown: false }}
    />
    <ProjectUpdatesStack.Screen
      name="AddProjectUpdate"
      component={AddProjectUpdateScreen}
      options={{ title: 'Create Update' }}
    />
    <ProjectUpdatesStack.Screen
      name="EditProjectUpdate"
      component={EditProjectUpdateScreen}
      options={{ title: 'Edit Update' }}
    />
  </ProjectUpdatesStack.Navigator>
);

const DirectoryStackNavigator = () => (
  <DirectoryStack.Navigator>
    <DirectoryStack.Screen
      name="DirectoryList"
      component={DirectoryScreen}
      options={{ title: 'Directory', headerShown: false }}
    />
    <DirectoryStack.Screen
      name="EmployeeDetails"
      component={EmployeeDetailsScreen}
      options={{ title: 'Employee Details', headerShown: false }}
    />
  </DirectoryStack.Navigator>
);

const DrawerContent = (props: any) => {
  const currentUser = supabaseAuthService.getCurrentUser();

  const handleSignOut = async () => {
    try {
      await supabaseAuthService.signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      {/* Header with Logo */}
      <View style={{
        backgroundColor: '#fff',
        paddingHorizontal: 20,
        paddingTop: 50,
        paddingBottom: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f1f5f9',
      }}>
        <Image
          source={require('../../assets/texam-logo.png')}
          style={{
            height: 32,
            width: 140,
            resizeMode: 'contain',
          }}
        />
      </View>

      <DrawerContentScrollView {...props} contentContainerStyle={{ paddingTop: 0 }}>
        <DrawerItemList {...props} />
        <DrawerItem
          label="Sign Out"
          onPress={handleSignOut}
          icon={({ color, size }) => (
            <MaterialIcons name="logout" size={size} color={color} />
          )}
          labelStyle={{ color: '#d32f2f', fontWeight: '500', fontSize: 14 }}
          style={{ marginTop: 20 }}
        />
      </DrawerContentScrollView>
    </View>
  );
};

const drawerIcons: { [key: string]: (color: string) => ReactNode } = {
  Dashboard: (color: string) => <MaterialCommunityIcons name="view-dashboard-outline" size={20} color={color} />,
  Directory: (color: string) => <MaterialCommunityIcons name="account-group-outline" size={20} color={color} />,
  'Team Management': (color: string) => <MaterialCommunityIcons name="account-multiple-outline" size={20} color={color} />,
  'Project Updates': (color: string) => <MaterialCommunityIcons name="clipboard-text-outline" size={20} color={color} />,
  'Project Management': (color: string) => <MaterialCommunityIcons name="folder-multiple-outline" size={20} color={color} />,
  'Manage Users': (color: string) => <MaterialCommunityIcons name="account-cog-outline" size={20} color={color} />,
  'Leave Management': (color: string) => <MaterialCommunityIcons name="calendar-check-outline" size={20} color={color} />,
  Announcements: (color: string) => <MaterialCommunityIcons name="bullhorn-outline" size={20} color={color} />,
};

const getRoleStack = (role: string | null) => {
  if (role === 'superadmin') return SuperadminStackNavigator;
  if (role === 'manager') return ManagerStackNavigator;
  if (role === 'employee') return EmployeeStackNavigator;
  return EmployeeStackNavigator;
};

const AppNavigator = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState<string | null>(null);
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    // Don't start loading until splash is finished
    if (!showSplash) {
      const timer = setTimeout(() => {
        console.log('App initialized');
        setLoading(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [showSplash]);

  // Initialize auth service and set up listener
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Initialize the auth service
        await supabaseAuthService.initialize();

        // Set up auth state listener
        const unsubscribe = supabaseAuthService.onAuthStateChanged((user) => {
          console.log('Auth state changed:', user ? 'User logged in' : 'User logged out');
          setUser(user);
          setRole(user?.role || null);

          if (user) {
            console.log('Set user role:', user.role);
          }
        });

        return unsubscribe;
      } catch (error) {
        console.error('Error initializing auth:', error);
      }
    };

    const unsubscribePromise = initializeAuth();

    return () => {
      unsubscribePromise.then(unsubscribe => {
        if (unsubscribe) unsubscribe();
      });
    };
  }, []);

  // Show splash screen first
  if (showSplash) {
    return <SplashScreen onFinish={() => setShowSplash(false)} />;
  }

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!user) {
    return (
      <NavigationContainer>
        <AuthStack />
      </NavigationContainer>
    );
  }

  if (!role) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  const RoleStack = getRoleStack(role);

  return (
    <NavigationContainer>
      <Drawer.Navigator
        drawerContent={DrawerContent}
        initialRouteName="Dashboard"
        screenOptions={({ navigation, route }) => ({
          header: () => <CustomAppbar navigation={navigation} />,
          drawerIcon: ({ color }) => drawerIcons[route.name] ? drawerIcons[route.name](color) : null,
          drawerActiveTintColor: '#1e40af',
          drawerInactiveTintColor: '#64748b',
          drawerActiveBackgroundColor: '#eff6ff',
          drawerInactiveBackgroundColor: 'transparent',
          drawerLabelStyle: {
            fontWeight: '500',
            fontSize: 14,
            marginLeft: -16,
          },
          drawerItemStyle: {
            borderRadius: 8,
            marginHorizontal: 12,
            marginVertical: 2,
            paddingHorizontal: 8,
          },
        })}
      >
        <Drawer.Screen name="Dashboard" component={RoleStack} options={{ headerShown: true }} />
        <Drawer.Screen name="Directory" component={DirectoryStackNavigator} options={{
          drawerLabel: 'Directory',
          drawerIcon: ({ color }) => drawerIcons['Directory'](color),
        }} />

        {/* Team Management - Only visible to Managers and Super Admins */}
        {(role === 'manager' || role === 'superadmin') && (
          <Drawer.Screen name="Team Management" component={TeamStackNavigator} options={{
            drawerLabel: 'Team Management',
            drawerIcon: ({ color }) => drawerIcons['Team Management'](color),
          }} />
        )}

        {/* Project Updates - Visible to all users */}
        <Drawer.Screen name="Project Updates" component={ProjectUpdatesStackNavigator} options={{
          drawerLabel: 'Project Updates',
          drawerIcon: ({ color }) => drawerIcons['Project Updates'](color),
        }} />

        <Drawer.Screen name="Leave Management" component={LeaveStackNavigator}
          options={{
            drawerLabel: 'Leave Management',
            drawerIcon: ({ color }) => drawerIcons['Leave Management'](color),
          }}
        />
        <Drawer.Screen name="Announcements" component={AnnouncementStackNavigator} />

        {/* Project Management - Only visible to Super Admins */}
        {role === 'superadmin' && (
          <Drawer.Screen name="Project Management" component={ProjectManagementScreen} options={{
            drawerLabel: 'Project Management',
            drawerIcon: ({ color }) => drawerIcons['Project Management'](color),
          }} />
        )}

        {/* Manage Users - Only visible to Super Admins */}
        {role === 'superadmin' && (
          <Drawer.Screen name="Manage Users" component={AdminPanelScreen} options={{
            drawerLabel: 'Manage Users',
            drawerIcon: ({ color }) => drawerIcons['Manage Users'](color),
          }} />
        )}
      </Drawer.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator; 