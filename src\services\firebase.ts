// Import polyfill FIRST before any Firebase imports
import 'react-native-get-random-values';

// Firebase v9 imports (Expo Go compatible) - Firestore and Storage
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { collection, addDoc, getDocs, query, where, updateDoc, doc, orderBy, deleteDoc } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCjvvPdl3-2If38IncLNwrpMXfZAknrN-M",
  authDomain: "texam-app.firebaseapp.com",
  projectId: "texam-app",
  storageBucket: "texam-app.appspot.com",
  messagingSenderId: "1042606359898",
  appId: "1:1042606359898:web:ad41564204674180e11f63",
  measurementId: "G-V1LDX2NKZD"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firestore and Storage
export const db = getFirestore(app);
export const storage = getStorage(app);

// Leave Management Functions
export const createLeaveRequest = async (leaveData: any) => {
  return await addDoc(collection(db, 'leaves'), leaveData);
};

export const getLeaveRequests = async (filters: any = {}) => {
  let q = collection(db, 'leaves');
  // Add filtering logic here if needed (e.g., by userId, status, etc.)
  // For now, just return all
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};

export const updateLeaveStatus = async (leaveId: string, status: string, approverId: string, approverName?: string) => {
  const updateData: any = {
    status,
    approverId,
    updatedAt: new Date().toISOString()
  };

  if (status === 'approved' || status === 'rejected') {
    updateData.approvedAt = new Date().toISOString();
    if (approverName) {
      updateData.approverName = approverName;
    }
  }

  return await updateDoc(doc(db, 'leaves', leaveId), updateData);
};

// Announcements Management
export const addAnnouncement = async (announcement: any) => {
  return await addDoc(collection(db, 'announcements'), announcement);
};

export const getAnnouncements = async () => {
  const q = query(collection(db, 'announcements'), orderBy('createdAt', 'desc'));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};

export const deleteAnnouncement = async (announcementId: string) => {
  return await deleteDoc(doc(db, 'announcements', announcementId));
}; 