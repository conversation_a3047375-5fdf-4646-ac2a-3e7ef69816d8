import { supabase } from './supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

class ImageUploadService {
  // Upload image to Supabase Storage
  async uploadImage(
    imageUri: string,
    imageName: string,
    folder: string = 'project-updates'
  ): Promise<{ url: string; path: string }> {
    try {
      // Create a unique file name
      const timestamp = Date.now();
      const fileName = `${timestamp}_${imageName}`;
      const filePath = `${folder}/${fileName}`;

      // Convert React Native file URI to blob using fetch
      const response = await fetch(imageUri);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }

      const blob = await response.blob();

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('images')
        .upload(filePath, blob, {
          contentType: blob.type || 'image/jpeg',
          upsert: false
        });

      if (error) {
        // Check if it's a bucket not found error
        if (error.message?.includes('Bucket not found')) {
          throw new Error('Storage bucket "images" not found. Please create it in Supabase dashboard.');
        }
        throw error;
      }

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('images')
        .getPublicUrl(data.path);

      return {
        url: urlData.publicUrl,
        path: data.path,
      };
    } catch (error) {
      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('Network request failed')) {
          throw new Error('Network error: Please check your internet connection and Supabase configuration.');
        }
        if (error.message.includes('Bucket not found')) {
          throw new Error('Storage bucket not configured. Please create "images" bucket in Supabase dashboard.');
        }
      }

      throw new Error('Failed to upload image');
    }
  }

  // Fallback: Store image locally (for demo purposes)
  async uploadImageLocally(
    imageUri: string,
    imageName: string,
    folder: string = 'project-updates'
  ): Promise<{ url: string; path: string }> {
    try {
      // Create a unique identifier
      const timestamp = Date.now();
      const fileName = `${timestamp}_${imageName}`;
      const localPath = `${folder}/${fileName}`;

      // Store the image URI in AsyncStorage with a unique key
      const storageKey = `image_${localPath}`;
      await AsyncStorage.setItem(storageKey, imageUri);

      // Return the original URI as the "URL" for local storage
      return {
        url: imageUri, // Use original URI for display
        path: localPath, // Store the logical path
      };
    } catch (error) {
      throw new Error('Failed to store image locally');
    }
  }

  // Enhanced upload with fallback
  async uploadImageWithFallback(
    imageUri: string,
    imageName: string,
    folder: string = 'project-updates'
  ): Promise<{ url: string; path: string }> {
    try {
      // Try Supabase first (silently)
      return await this.uploadImage(imageUri, imageName, folder);
    } catch (supabaseError) {
      // Silently fall back to local storage
      try {
        return await this.uploadImageLocally(imageUri, imageName, folder);
      } catch (localError) {
        // Final fallback - return original URI
        return {
          url: imageUri, // Use original URI as fallback
          path: `fallback/${Date.now()}_${imageName}`,
        };
      }
    }
  }

  // Delete image from Supabase Storage
  async deleteImage(imagePath: string): Promise<void> {
    try {
      const { error } = await supabase.storage
        .from('images')
        .remove([imagePath]);

      if (error) {
        console.error('Supabase storage delete error:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      throw new Error('Failed to delete image');
    }
  }

  // Upload multiple images
  async uploadMultipleImages(
    images: Array<{ uri: string; name: string }>,
    folder: string = 'project-updates'
  ): Promise<Array<{ url: string; path: string; name: string }>> {
    try {
      const uploadPromises = images.map(async (image) => {
        const result = await this.uploadImage(image.uri, image.name, folder);
        return {
          ...result,
          name: image.name,
        };
      });

      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Error uploading multiple images:', error);
      throw new Error('Failed to upload images');
    }
  }

  // Get image URL from path (if needed)
  async getImageUrl(imagePath: string): Promise<string> {
    try {
      const { data } = supabase.storage
        .from('images')
        .getPublicUrl(imagePath);

      return data.publicUrl;
    } catch (error) {
      console.error('Error getting image URL:', error);
      throw new Error('Failed to get image URL');
    }
  }

  // Validate image file
  validateImage(imageUri: string, maxSizeInMB: number = 5): boolean {
    // Basic validation - in a real app you might want more sophisticated validation
    if (!imageUri) return false;
    
    // Check file extension (basic check)
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const hasValidExtension = validExtensions.some(ext => 
      imageUri.toLowerCase().includes(ext)
    );
    
    return hasValidExtension;
  }

  // Generate unique filename
  generateFileName(originalName: string, prefix: string = ''): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop() || 'jpg';
    
    return `${prefix}${timestamp}_${randomString}.${extension}`;
  }

  // Compress image (basic implementation)
  async compressImage(imageUri: string, quality: number = 0.8): Promise<string> {
    // Note: This is a placeholder. In a real app, you might use a library like
    // expo-image-manipulator for actual image compression
    try {
      // For now, just return the original URI
      // In production, you would implement actual compression here
      return imageUri;
    } catch (error) {
      console.error('Error compressing image:', error);
      return imageUri; // Return original if compression fails
    }
  }
}

export const imageUploadService = new ImageUploadService();
