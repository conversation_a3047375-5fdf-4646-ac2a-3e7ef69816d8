import { db } from './supabase';
import { supabaseAuthService } from './supabaseAuth';
import {
  Project,
  ProjectUpdate,
  ProjectUpdateFormData,
  ProjectUpdateFilters,
  CreateProjectData,
  getUpdateScope
} from '../types/projectUpdates';
import { imageUploadService } from './imageUploadService';

class ProjectUpdatesService {
  // Project Management (Super Admin only)
  async createProject(projectData: CreateProjectData): Promise<string> {
    try {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) {
        throw new Error('No current user found');
      }

      const project = {
        name: projectData.name,
        description: projectData.description,
        location: projectData.location,
        status: projectData.status || 'active',
        team_ids: projectData.teamIds || [],
        created_by: currentUser.id,
      };

      const result = await db.projects.create(project);
      return result.id;
    } catch (error) {
      console.error('Error creating project:', error);
      throw new Error('Failed to create project');
    }
  }

  async getProjects(): Promise<Project[]> {
    try {
      const data = await db.projects.getAll();

      return data.map((project: any) => ({
        id: project.id,
        name: project.name,
        description: project.description,
        location: project.location,
        createdBy: project.created_by,
        createdAt: new Date(project.created_at),
        status: project.status || 'active',
        teamIds: project.team_ids || [],
      }));
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw new Error('Failed to fetch projects');
    }
  }

  async getProjectById(projectId: string): Promise<Project | null> {
    try {
      const project = await db.projects.getById(projectId);

      if (project) {
        return {
          id: project.id,
          name: project.name,
          description: project.description,
          createdBy: project.created_by,
          createdAt: new Date(project.created_at),
          status: project.status || 'active',
          teamIds: project.team_ids || [],
        };
      }

      return null;
    } catch (error) {
      console.error('Error fetching project:', error);
      throw new Error('Failed to fetch project');
    }
  }

  async updateProject(projectId: string, updates: Partial<CreateProjectData>): Promise<void> {
    try {
      const updateData: any = {};

      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.location !== undefined) updateData.location = updates.location;
      if (updates.status !== undefined) updateData.status = updates.status;
      if (updates.teamIds !== undefined) updateData.team_ids = updates.teamIds;

      await db.projects.update(projectId, updateData);
    } catch (error) {
      console.error('Error updating project:', error);
      throw new Error('Failed to update project');
    }
  }

  async deleteProject(projectId: string): Promise<void> {
    try {
      // Note: In a real app, you might want to soft delete or check for dependencies
      await db.projects.delete(projectId);
    } catch (error) {
      console.error('Error deleting project:', error);
      throw new Error('Failed to delete project');
    }
  }

  // Project Updates Management
  async createProjectUpdate(
    updateData: ProjectUpdateFormData,
    creator: {
      id: string;
      name: string;
      role: 'manager' | 'superadmin';
    }
  ): Promise<string> {
    try {
      console.log('ProjectUpdatesService: Starting createProjectUpdate');
      console.log('Update data:', updateData);
      console.log('Creator:', creator);

      // Get project details
      console.log('Fetching project with ID:', updateData.projectId);
      const project = await this.getProjectById(updateData.projectId);
      if (!project) {
        throw new Error('Project not found');
      }
      console.log('Project found:', project);

      // Determine visibility scope
      const scope = getUpdateScope(creator.role, creator.id);

      // Handle image upload to Supabase Storage
      let imageUrl: string | undefined;
      let imageName: string | undefined;

      if (updateData.image) {
        try {
          const uploadResult = await imageUploadService.uploadImageWithFallback(
            updateData.image.uri,
            updateData.image.name,
            'project-updates'
          );
          imageUrl = uploadResult.url;
          imageName = updateData.image.name;
        } catch (error) {
          // Continue without image if all methods fail (very rare)
        }
      }

      const projectUpdate = {
        project_id: updateData.projectId,
        project_name: project.name,
        heading: updateData.heading,
        description: updateData.description,
        created_by: creator.id,
        created_by_name: creator.name,
        created_by_role: creator.role,
        visible_to_all: scope.visibleToAll,
        // Only include manager_id if it exists (for manager updates)
        ...(scope.managerId && { manager_id: scope.managerId }),
        // Only include imageUrl and imageName if they exist
        ...(imageUrl && { image_url: imageUrl }),
        ...(imageName && { image_name: imageName }),
      };

      console.log('Saving project update to Supabase:', projectUpdate);
      const result = await db.projectUpdates.create(projectUpdate);

      console.log('Project update saved successfully with ID:', result.id);
      return result.id;
    } catch (error) {
      console.error('Error creating project update:', error);
      console.error('Error details:', error.message, error.stack);
      throw new Error(`Failed to create project update: ${error.message}`);
    }
  }

  async getProjectUpdates(filters: ProjectUpdateFilters): Promise<ProjectUpdate[]> {
    try {
      const queryFilters: any = {};

      // Apply project filter if specified
      if (filters.projectId) {
        queryFilters.projectId = filters.projectId;
      }

      const data = await db.projectUpdates.getAll(queryFilters);
      const allUpdates: ProjectUpdate[] = data.map((update: any) => ({
        id: update.id,
        projectId: update.project_id,
        projectName: update.project_name,
        heading: update.heading,
        description: update.description,
        imageUrl: update.image_url,
        imageName: update.image_name,
        createdBy: update.created_by,
        createdByName: update.created_by_name,
        createdByRole: update.created_by_role,
        createdAt: new Date(update.created_at),
        updatedAt: new Date(update.updated_at),
        visibleToTeamIds: [], // Not used in current implementation
        visibleToAll: update.visible_to_all || false,
        managerId: update.manager_id,
      }));

      // Filter based on user role and permissions
      return this.filterUpdatesByVisibility(allUpdates, filters);
    } catch (error) {
      console.error('Error fetching project updates:', error);
      throw new Error('Failed to fetch project updates');
    }
  }

  private filterUpdatesByVisibility(
    updates: ProjectUpdate[], 
    filters: ProjectUpdateFilters
  ): ProjectUpdate[] {
    const { userRole, userId, managerId } = filters;

    return updates.filter(update => {
      // Super admin can see everything
      if (userRole === 'superadmin') {
        return true;
      }

      // Manager can see their own updates and superadmin updates
      if (userRole === 'manager') {
        return update.visibleToAll || update.createdBy === userId;
      }

      // Employee can see superadmin updates and updates from their manager
      if (userRole === 'employee') {
        return update.visibleToAll || update.managerId === managerId;
      }

      return false;
    });
  }

  async updateProjectUpdate(
    updateId: string,
    updateData: ProjectUpdateFormData,
    creator: { id: string; name: string; role: 'manager' | 'superadmin' }
  ): Promise<void> {
    try {
      console.log('ProjectUpdatesService: Starting updateProjectUpdate');
      console.log('Update ID:', updateId);
      console.log('Update data:', updateData);
      console.log('Creator:', creator);

      // Verify ownership or admin privileges
      const existingUpdate = await this.getProjectUpdateById(updateId);
      if (!existingUpdate) {
        throw new Error('Update not found');
      }

      // Only allow creator or superadmin to edit
      if (existingUpdate.createdBy !== creator.id && creator.role !== 'superadmin') {
        throw new Error('Unauthorized to edit this update');
      }

      // Handle image upload if provided
      let imageUrl: string | undefined = existingUpdate.imageUrl;
      let imageName: string | undefined = existingUpdate.imageName;

      if (updateData.image) {
        try {
          const uploadResult = await imageUploadService.uploadImageWithFallback(
            updateData.image.uri,
            updateData.image.name,
            'project-updates'
          );
          imageUrl = uploadResult.url;
          imageName = updateData.image.name;
        } catch (error) {
          // Continue with existing image if upload fails
        }
      }

      // Prepare update fields
      const updateFields: any = {
        heading: updateData.heading,
        description: updateData.description,
        updated_at: new Date().toISOString(),
      };

      // Only update image fields if we have new image data
      if (imageUrl) updateFields.image_url = imageUrl;
      if (imageName) updateFields.image_name = imageName;

      console.log('Updating with fields:', updateFields);

      await db.projectUpdates.update(updateId, updateFields);
      console.log('Project update updated successfully');
    } catch (error) {
      console.error('Error updating project update:', error);
      throw new Error('Failed to update project update');
    }
  }

  async deleteProjectUpdate(updateId: string, userId: string, userRole: string): Promise<void> {
    try {
      // Verify permissions
      const updateDoc = await this.getProjectUpdateById(updateId);
      if (!updateDoc) {
        throw new Error('Update not found');
      }

      // Super admin can delete any update, others can only delete their own
      if (userRole !== 'superadmin' && updateDoc.createdBy !== userId) {
        throw new Error('Unauthorized to delete this update');
      }

      const docRef = doc(db, this.updatesCollection, updateId);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting project update:', error);
      throw new Error('Failed to delete project update');
    }
  }

  async getProjectUpdateById(updateId: string): Promise<ProjectUpdate | null> {
    try {
      console.log('Fetching project update by ID:', updateId);
      const data = await db.projectUpdates.getById(updateId);

      if (!data) {
        console.log('No project update found with ID:', updateId);
        return null;
      }

      console.log('Raw project update data:', data);

      const projectUpdate = {
        id: data.id,
        projectId: data.project_id,
        projectName: data.project_name,
        heading: data.heading,
        description: data.description,
        imageUrl: data.image_url,
        imageName: data.image_name,
        createdBy: data.created_by,
        createdByName: data.created_by_name,
        createdByRole: data.created_by_role,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at || data.created_at),
        visibleToTeamIds: data.visible_to_team_ids || [],
        visibleToAll: data.visible_to_all || false,
        managerId: data.manager_id,
      };

      console.log('Transformed project update:', projectUpdate);
      return projectUpdate;
    } catch (error) {
      console.error('Error fetching project update:', error);
      throw new Error('Failed to fetch project update');
    }
  }

  // Helper method to get updates for a specific user
  async getUpdatesForUser(
    userId: string, 
    userRole: 'employee' | 'manager' | 'superadmin',
    managerId?: string
  ): Promise<ProjectUpdate[]> {
    const filters: ProjectUpdateFilters = {
      userRole,
      userId,
      managerId,
    };

    return this.getProjectUpdates(filters);
  }
}

export const projectUpdatesService = new ProjectUpdatesService();
