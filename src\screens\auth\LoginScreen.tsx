import React, { useState } from 'react';
import { View, Image, ScrollView, StyleSheet, Dimensions, StatusBar } from 'react-native';
import { Text, TextInput, Button, IconButton } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

type AuthStackParamList = {
  Login: undefined;
  Signup: undefined;
};

type Props = NativeStackScreenProps<AuthStackParamList, 'Login'>;

const LoginScreen: React.FC<Props> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    setLoading(true);
    setError('');
    try {
      await supabaseAuthService.signIn(email, password);
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header with Logo and Icons */}
      <View style={styles.header}>
        <Image
          source={require('../../../assets/texam-logo.png')}
          style={styles.headerLogo}
          resizeMode="contain"
        />
        <View style={styles.headerIcons}>
          <IconButton
            icon="bell-outline"
            size={24}
            iconColor="#666"
            onPress={() => {/* Handle notifications */}}
          />
          <IconButton
            icon="account-circle-outline"
            size={24}
            iconColor="#666"
            onPress={() => {/* Handle profile */}}
          />
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Main Content */}
        <View style={styles.content}>
          <Text style={styles.title}>WorkFlow Management Suite</Text>
          <Text style={styles.subtitle}>
            A unified platform for managing information flow by integrating tools & systems to improve efficiency.
          </Text>

          {/* Sign In Form */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Login to Access WorkFlow</Text>

            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
              left={<TextInput.Icon icon="email-outline" />}
              outlineColor="#e0e0e0"
              activeOutlineColor="#00a9e9"
            />

            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              secureTextEntry={!showPassword}
              style={styles.input}
              left={<TextInput.Icon icon="lock-outline" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? "eye-off-outline" : "eye-outline"}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              outlineColor="#e0e0e0"
              activeOutlineColor="#00a9e9"
            />

            {error ? <Text style={styles.errorText}>{error}</Text> : null}

            <Button
              mode="contained"
              onPress={handleLogin}
              loading={loading}
              disabled={loading}
              style={styles.signInButton}
              labelStyle={styles.signInButtonText}
              icon="arrow-right"
              contentStyle={styles.signInButtonContent}
            >
              Sign In
            </Button>

            <Button
              mode="outlined"
              onPress={() => {/* Handle forgot password */}}
              style={styles.resetButton}
              labelStyle={styles.resetButtonText}
              icon="email-outline"
            >
              Reset Password
            </Button>

            <View style={styles.signUpContainer}>
              <Text style={styles.newToWorkflow}>NEW TO WORKFLOW?</Text>
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Signup')}
                style={styles.createAccountButton}
                labelStyle={styles.createAccountButtonText}
                icon="arrow-right"
              >
                Create Account
              </Button>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: '#ffffff',
  },
  headerLogo: {
    height: 40,
    width: width * 0.4,
  },
  headerIcons: {
    flexDirection: 'row',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e3a8a',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  formContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 24,
    padding: 24,
    marginTop: 10,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
  },
  input: {
    marginBottom: 16,
    backgroundColor: '#ffffff',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  signInButton: {
    backgroundColor: '#1e397e',
    borderRadius: 12,
    marginBottom: 16,
  },
  signInButtonContent: {
    height: 48,
    flexDirection: 'row-reverse',
  },
  signInButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  resetButton: {
    borderColor: '#00a9e9',
    borderRadius: 12,
    marginBottom: 24,
  },
  resetButtonText: {
    color: '#00a9e9',
    fontSize: 16,
  },
  signUpContainer: {
    alignItems: 'center',
    color: '#666',
  },
  newToWorkflow: {
    fontSize: 12,
    color: '#999',
    fontWeight: '600',
    letterSpacing: 1,
    marginBottom: 12,
  },
  createAccountButton: {
    borderColor: '#999',
    borderRadius: 12,
    width: '100%',
  },
  createAccountButtonText: {
    color: '#666',
    fontSize: 16,
  },
});

export default LoginScreen;