import React, { useState } from 'react';
import { View, Alert } from 'react-native';
import { Text, Button, TextInput, HelperText, Checkbox } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { supabase, db } from '../../services/supabase';
import { DEPARTMENTS } from '../../utils/departments';

const AnnouncementAddScreen = ({ navigation }: any) => {
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [departments, setDepartments] = useState<string[]>(['All Departments']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const toggleDepartment = (dept: string) => {
    if (dept === 'All Departments') {
      setDepartments(['All Departments']);
    } else {
      let newDepts = departments.includes(dept)
        ? departments.filter(d => d !== dept)
        : [...departments.filter(d => d !== 'All Departments'), dept];
      setDepartments(newDepts.length === 0 ? ['All Departments'] : newDepts);
    }
  };

  const handleSubmit = async () => {
    if (!title.trim() || !message.trim()) {
      setError('Title and message are required');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) {
        throw new Error('Not authenticated');
      }

      // Get user profile to get the name
      const userProfile = await db.users.getById(currentUser.id);
      const userName = userProfile?.name || userProfile?.email || 'Unknown User';

      const { error } = await supabase
        .from('announcements')
        .insert({
          title: title.trim(),
          content: message.trim(), // Using 'content' column from database
          departments: departments,
          department: departments.includes('All Departments') ? 'All Departments' : departments[0], // Also set single department
          created_by: currentUser.id,
          created_by_name: userName, // Add the required created_by_name field
        });

      if (error) {
        throw error;
      }

      Alert.alert('Success', 'Announcement created successfully');
      navigation.goBack();
    } catch (e: any) {
      console.error('Error creating announcement:', e);
      setError(e.message || 'Failed to create announcement');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <TextInput label="Title" value={title} onChangeText={setTitle} style={{ marginBottom: 8 }} />
      <TextInput label="Message" value={message} onChangeText={setMessage} multiline style={{ marginBottom: 8 }} />
      <Text style={{ marginTop: 8, marginBottom: 4, fontWeight: 'bold' }}>Target Departments</Text>
      {DEPARTMENTS.map(dept => (
        <View key={dept} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
          <Checkbox
            status={departments.includes(dept) ? 'checked' : 'unchecked'}
            onPress={() => toggleDepartment(dept)}
          />
          <Text onPress={() => toggleDepartment(dept)}>{dept}</Text>
        </View>
      ))}
      {error ? <HelperText type="error">{error}</HelperText> : null}
      <Button mode="contained" onPress={handleSubmit} loading={loading} disabled={loading} style={{ marginTop: 16 }}>Submit</Button>
      <Button onPress={() => navigation.goBack()} style={{ marginTop: 8 }}>Cancel</Button>
    </View>
  );
};

export default AnnouncementAddScreen; 