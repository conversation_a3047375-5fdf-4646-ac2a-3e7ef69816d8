import React, { useEffect, useState } from 'react';
import { View, ScrollView, Image, Linking, TouchableOpacity } from 'react-native';
import { Text, ActivityIndicator, List, TextInput, Menu, Button, Card, Portal, Dialog, Divider, IconButton } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  phone?: string;
  photoURL?: string;
}

const DirectoryScreen = ({ navigation }: any) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [deptMenuVisible, setDeptMenuVisible] = useState(false);
  const [roleMenuVisible, setRoleMenuVisible] = useState(false);
  const [currentUserRole, setCurrentUserRole] = useState<string>('employee');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [profileDialogVisible, setProfileDialogVisible] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;

      // Get current user's role
      const userRole = currentUser.role || 'employee';
      setCurrentUserRole(userRole);

      // Get all users from Supabase
      const usersData = await db.users.getAll();
      const usersList: User[] = usersData.map((userData: any) => ({
        id: userData.id,
        name: userData.name || '',
        email: userData.email || '',
        role: userData.role || 'employee',
        department: userData.department || '',
        phone: userData.phone || '',
        photoURL: userData.photo_url || '',
      }));

      // Sort users by name
      usersList.sort((a, b) => a.name.localeCompare(b.name));
      setUsers(usersList);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get unique departments for filter
  const departments = Array.from(new Set(users.map(u => u.department || '').filter(Boolean)));
  
  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const searchMatch = 
      user.name.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase()) ||
      (user.department && user.department.toLowerCase().includes(search.toLowerCase()));
    
    const deptMatch = departmentFilter === 'all' || user.department === departmentFilter;
    const roleMatch = roleFilter === 'all' || user.role === roleFilter;
    
    return searchMatch && deptMatch && roleMatch;
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin': return '#dc2626';
      case 'manager': return '#f59e0b';
      case 'employee': return '#1e3a8a';
      default: return '#666';
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'superadmin': return '#ef4444'; // Red for Admin
      case 'manager': return '#f59e0b';    // Yellow for Manager
      case 'employee': return '#3b82f6';   // Blue for Employee
      default: return '#6b7280';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superadmin': return 'shield-crown';
      case 'manager': return 'account-tie';
      case 'employee': return 'account';
      default: return 'account';
    }
  };

  const handleUserPress = (user: User) => {
    setSelectedUser(user);
    setProfileDialogVisible(true);
  };

  const handleEmailPress = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  const handlePhonePress = (phone: string) => {
    Linking.openURL(`tel:${phone}`);
  };

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 16 }}>Loading directory...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f8f9fa' }}>
      {/* Header */}
      <View style={{ backgroundColor: '#fff', paddingHorizontal: 20, paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
        <Text style={{ fontSize: 18, fontWeight: '600', color: '#1f2937' }}>
          Employee Directory
        </Text>
      </View>

      <View style={{ padding: 20 }}>
        {/* Search Bar */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: '#fff',
          borderRadius: 8,
          borderWidth: 1,
          borderColor: '#d1d5db',
          paddingHorizontal: 12,
          marginBottom: 16
        }}>
          <MaterialCommunityIcons name="magnify" size={20} color="#9ca3af" />
          <TextInput
            placeholder="Search with name / email / designation..."
            value={search}
            onChangeText={setSearch}
            style={{
              flex: 1,
              marginLeft: 8,
              fontSize: 14,
              backgroundColor: 'transparent',
              paddingVertical: 12
            }}
            mode="flat"
            underlineStyle={{ display: 'none' }}
            contentStyle={{ backgroundColor: 'transparent' }}
          />
        </View>

        {/* Results count */}
        <Text style={{ color: '#6b7280', fontSize: 14, marginBottom: 16 }}>
          {filteredUsers.length} People found
        </Text>
      </View>

      {/* User List */}
      <ScrollView style={{ flex: 1, paddingHorizontal: 20 }} showsVerticalScrollIndicator={false}>
        {filteredUsers.length === 0 ? (
          <View style={{ padding: 32, alignItems: 'center' }}>
            <MaterialCommunityIcons name="account-search" size={64} color="#d1d5db" />
            <Text style={{ color: '#6b7280', marginTop: 16, textAlign: 'center' }}>
              No users found matching your search criteria
            </Text>
          </View>
        ) : (
          filteredUsers.map((user) => (
            <TouchableOpacity key={user.id} onPress={() => handleUserPress(user)}>
              <View style={{
                backgroundColor: '#fff',
                borderRadius: 8,
                padding: 16,
                marginBottom: 12,
                flexDirection: 'row',
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.05,
                shadowRadius: 2,
                elevation: 1,
              }}>
                {/* Profile Image */}
                <View style={{ marginRight: 12 }}>
                  {user.photoURL ? (
                    <Image
                      source={{ uri: user.photoURL }}
                      style={{ width: 48, height: 48, borderRadius: 24 }}
                    />
                  ) : (
                    <View style={{
                      width: 48,
                      height: 48,
                      borderRadius: 24,
                      backgroundColor: '#e5e7eb',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}>
                      <MaterialCommunityIcons
                        name="account"
                        size={24}
                        color="#9ca3af"
                      />
                    </View>
                  )}
                </View>

                {/* User Info */}
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', color: '#1f2937', marginBottom: 2 }}>
                    {user.name || user.email}
                  </Text>
                  <Text style={{ fontSize: 14, color: '#6b7280', marginBottom: 2 }}>
                    Email: {user.email}
                  </Text>
                  {user.phone && (
                    <Text style={{ fontSize: 14, color: '#6b7280', marginBottom: 2 }}>
                      Mobile: {user.phone}
                    </Text>
                  )}
                  <Text style={{ fontSize: 14, color: '#6b7280' }}>
                    Department: {user.department || 'Not specified'}
                  </Text>
                </View>

                {/* Role Badge */}
                <View style={{
                  backgroundColor: getRoleBadgeColor(user.role),
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  borderRadius: 4,
                }}>
                  <Text style={{
                    color: '#fff',
                    fontSize: 12,
                    fontWeight: '500',
                    textTransform: 'capitalize'
                  }}>
                    {user.role === 'superadmin' ? 'Admin' : user.role}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>

      {/* Profile Detail Dialog */}
      <Portal>
        <Dialog
          visible={profileDialogVisible}
          onDismiss={() => setProfileDialogVisible(false)}
          style={{ maxHeight: '85%', borderRadius: 12 }}
        >
          <Dialog.Title style={{
            textAlign: 'center',
            paddingBottom: 8,
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937'
          }}>
            Employee Details
          </Dialog.Title>

          {selectedUser && (
            <Dialog.Content style={{ paddingHorizontal: 20 }}>
              <ScrollView showsVerticalScrollIndicator={false}>
                {/* Profile Photo and Name */}
                <View style={{ alignItems: 'center', marginBottom: 24 }}>
                  {selectedUser.photoURL ? (
                    <Image
                      source={{ uri: selectedUser.photoURL }}
                      style={{ width: 80, height: 80, borderRadius: 40, marginBottom: 16 }}
                    />
                  ) : (
                    <View style={{
                      width: 80,
                      height: 80,
                      borderRadius: 40,
                      backgroundColor: '#e5e7eb',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: 16
                    }}>
                      <MaterialCommunityIcons
                        name="account"
                        size={40}
                        color="#9ca3af"
                      />
                    </View>
                  )}

                  <Text style={{ fontSize: 20, fontWeight: '600', textAlign: 'center', color: '#1f2937', marginBottom: 8 }}>
                    {selectedUser.name || 'No Name'}
                  </Text>

                  <View style={{
                    backgroundColor: getRoleBadgeColor(selectedUser.role),
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 6,
                  }}>
                    <Text style={{
                      color: '#fff',
                      fontSize: 14,
                      fontWeight: '500',
                      textTransform: 'capitalize'
                    }}>
                      {selectedUser.role === 'superadmin' ? 'Admin' : selectedUser.role}
                    </Text>
                  </View>
                </View>

                {/* Contact Information */}
                <View style={{ marginBottom: 20 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', marginBottom: 12, color: '#374151' }}>
                    Contact Information
                  </Text>

                  {/* Email */}
                  <TouchableOpacity
                    onPress={() => handleEmailPress(selectedUser.email)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      backgroundColor: '#f9fafb',
                      borderRadius: 8,
                      marginBottom: 8,
                      borderWidth: 1,
                      borderColor: '#e5e7eb'
                    }}
                  >
                    <MaterialCommunityIcons name="email-outline" size={18} color="#6b7280" />
                    <View style={{ marginLeft: 12, flex: 1 }}>
                      <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 2 }}>Email</Text>
                      <Text style={{ fontSize: 14, color: '#1f2937', fontWeight: '500' }}>
                        {selectedUser.email}
                      </Text>
                    </View>
                    <MaterialCommunityIcons name="open-in-new" size={16} color="#9ca3af" />
                  </TouchableOpacity>

                  {/* Phone */}
                  {selectedUser.phone && (
                    <TouchableOpacity
                      onPress={() => handlePhonePress(selectedUser.phone!)}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: 12,
                        paddingHorizontal: 16,
                        backgroundColor: '#f9fafb',
                        borderRadius: 8,
                        marginBottom: 8,
                        borderWidth: 1,
                        borderColor: '#e5e7eb'
                      }}
                    >
                      <MaterialCommunityIcons name="phone-outline" size={18} color="#6b7280" />
                      <View style={{ marginLeft: 12, flex: 1 }}>
                        <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 2 }}>Mobile</Text>
                        <Text style={{ fontSize: 14, color: '#1f2937', fontWeight: '500' }}>
                          {selectedUser.phone}
                        </Text>
                      </View>
                      <MaterialCommunityIcons name="open-in-new" size={16} color="#9ca3af" />
                    </TouchableOpacity>
                  )}
                </View>

                {/* Department Information */}
                <View style={{ marginBottom: 20 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', marginBottom: 12, color: '#374151' }}>
                    Department
                  </Text>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    backgroundColor: '#f9fafb',
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: '#e5e7eb'
                  }}>
                    <MaterialCommunityIcons name="office-building-outline" size={18} color="#6b7280" />
                    <View style={{ marginLeft: 12, flex: 1 }}>
                      <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 2 }}>Department</Text>
                      <Text style={{ fontSize: 14, color: '#1f2937', fontWeight: '500' }}>
                        {selectedUser.department || 'Not specified'}
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Role Information */}
                <View style={{ marginBottom: 20 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', marginBottom: 12, color: '#374151' }}>
                    Role & Access Level
                  </Text>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    backgroundColor: '#f9fafb',
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: '#e5e7eb'
                  }}>
                    <MaterialCommunityIcons
                      name="account-circle-outline"
                      size={18}
                      color="#6b7280"
                    />
                    <View style={{ marginLeft: 12, flex: 1 }}>
                      <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 2 }}>Role</Text>
                      <Text style={{ fontSize: 14, color: '#1f2937', fontWeight: '500' }}>
                        {selectedUser.role === 'superadmin' ? 'Admin' : selectedUser.role.charAt(0).toUpperCase() + selectedUser.role.slice(1)}
                      </Text>
                      <Text style={{ fontSize: 12, color: '#6b7280', marginTop: 2 }}>
                        {selectedUser.role === 'superadmin' && 'Full system access and user management'}
                        {selectedUser.role === 'manager' && 'Team management and approval permissions'}
                        {selectedUser.role === 'employee' && 'Standard user access'}
                      </Text>
                    </View>
                  </View>
                </View>
              </ScrollView>
            </Dialog.Content>
          )}

          <Dialog.Actions style={{ paddingHorizontal: 20, paddingBottom: 20 }}>
            <Button
              mode="contained"
              onPress={() => setProfileDialogVisible(false)}
              style={{
                backgroundColor: '#1e40af',
                borderRadius: 8,
                flex: 1
              }}
              labelStyle={{ fontSize: 14, fontWeight: '500' }}
            >
              Close
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

export default DirectoryScreen;
