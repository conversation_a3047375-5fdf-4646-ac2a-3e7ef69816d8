import React, { useEffect, useState } from 'react';
import { View, ScrollView, Image, Linking, TouchableOpacity } from 'react-native';
import { Text, ActivityIndicator, TextInput, Menu, Button } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  phone?: string;
  photoURL?: string;
}

const DirectoryScreen = ({ navigation }: any) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [deptMenuVisible, setDeptMenuVisible] = useState(false);
  const [roleMenuVisible, setRoleMenuVisible] = useState(false);
  const [currentUserRole, setCurrentUserRole] = useState<string>('employee');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;

      // Get current user's role
      const userRole = currentUser.role || 'employee';
      setCurrentUserRole(userRole);

      // Get all users from Supabase
      const usersData = await db.users.getAll();
      const usersList: User[] = usersData.map((userData: any) => ({
        id: userData.id,
        name: userData.name || '',
        email: userData.email || '',
        role: userData.role || 'employee',
        department: userData.department || '',
        phone: userData.phone || '',
        photoURL: userData.photo_url || '',
      }));

      // Sort users by name
      usersList.sort((a, b) => a.name.localeCompare(b.name));
      setUsers(usersList);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get unique departments for filter
  const departments = Array.from(new Set(users.map(u => u.department || '').filter(Boolean)));
  
  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const searchMatch = 
      user.name.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase()) ||
      (user.department && user.department.toLowerCase().includes(search.toLowerCase()));
    
    const deptMatch = departmentFilter === 'all' || user.department === departmentFilter;
    const roleMatch = roleFilter === 'all' || user.role === roleFilter;
    
    return searchMatch && deptMatch && roleMatch;
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin': return '#dc2626';
      case 'manager': return '#f59e0b';
      case 'employee': return '#1e3a8a';
      default: return '#666';
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'superadmin': return '#ef4444'; // Red for Admin
      case 'manager': return '#f59e0b';    // Yellow for Manager
      case 'employee': return '#3b82f6';   // Blue for Employee
      default: return '#6b7280';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superadmin': return 'shield-crown';
      case 'manager': return 'account-tie';
      case 'employee': return 'account';
      default: return 'account';
    }
  };

  const handleUserPress = (user: User) => {
    navigation.navigate('EmployeeDetails', { user });
  };

  const handleEmailPress = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  const handlePhonePress = (phone: string) => {
    Linking.openURL(`tel:${phone}`);
  };

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 16 }}>Loading directory...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f8f9fa' }}>
      {/* Header */}
      <View style={{ backgroundColor: '#fff', paddingHorizontal: 20, paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#e5e7eb' }}>
        <Text style={{ fontSize: 18, fontWeight: '600', color: '#1f2937' }}>
          Employee Directory
        </Text>
      </View>

      <View style={{ padding: 20 }}>
        {/* Search Bar */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: '#fff',
          borderRadius: 8,
          borderWidth: 1,
          borderColor: '#d1d5db',
          paddingHorizontal: 12,
          paddingVertical: 8,
          marginBottom: 16,
          height: 44
        }}>
          <MaterialCommunityIcons name="magnify" size={18} color="#9ca3af" />
          <TextInput
            placeholder="Search with name / email / designation..."
            value={search}
            onChangeText={setSearch}
            style={{
              flex: 1,
              marginLeft: 8,
              fontSize: 13,
              backgroundColor: 'transparent',
              height: 28
            }}
            mode="flat"
            underlineStyle={{ display: 'none' }}
            contentStyle={{
              backgroundColor: 'transparent',
              paddingVertical: 0
            }}
          />
        </View>

        {/* Results count */}
        <Text style={{ color: '#6b7280', fontSize: 13, marginBottom: 16, fontWeight: '500' }}>
          {filteredUsers.length} People found
        </Text>
      </View>

      {/* User List */}
      <ScrollView style={{ flex: 1, paddingHorizontal: 20 }} showsVerticalScrollIndicator={false}>
        {filteredUsers.length === 0 ? (
          <View style={{ padding: 32, alignItems: 'center' }}>
            <MaterialCommunityIcons name="account-search" size={64} color="#d1d5db" />
            <Text style={{ color: '#6b7280', marginTop: 16, textAlign: 'center' }}>
              No users found matching your search criteria
            </Text>
          </View>
        ) : (
          filteredUsers.map((user) => (
            <TouchableOpacity key={user.id} onPress={() => handleUserPress(user)}>
              <View style={{
                backgroundColor: '#fff',
                borderRadius: 8,
                padding: 12,
                marginBottom: 10,
                flexDirection: 'row',
                alignItems: 'flex-start',
                borderWidth: 1,
                borderColor: '#e5e7eb',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.05,
                shadowRadius: 2,
                elevation: 1,
              }}>
                {/* Profile Image and Badge */}
                <View style={{ marginRight: 12, alignItems: 'center', width: 80 }}>
                  {user.photoURL ? (
                    <Image
                      source={{ uri: user.photoURL }}
                      style={{ width: 42, height: 42, borderRadius: 21, marginBottom: 6 }}
                    />
                  ) : (
                    <View style={{
                      width: 42,
                      height: 42,
                      borderRadius: 21,
                      backgroundColor: '#e5e7eb',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: 6
                    }}>
                      <MaterialCommunityIcons
                        name="account"
                        size={22}
                        color="#9ca3af"
                      />
                    </View>
                  )}
                  {/* Role Badge under photo */}
                  <View style={{
                    backgroundColor: getRoleBadgeColor(user.role),
                    paddingHorizontal: 8,
                    paddingVertical: 3,
                    borderRadius: 6,
                    marginTop: 2,
                    minWidth: 70,
                    alignItems: 'center'
                  }}>
                    <Text style={{
                      color: '#fff',
                      fontSize: 10,
                      fontWeight: '500',
                      textTransform: 'capitalize'
                    }}>
                      {user.role === 'superadmin' ? 'Admin' : user.role}
                    </Text>
                  </View>
                </View>

                {/* User Info */}
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1f2937', marginBottom: 2 }}>
                    {user.name || user.email}
                  </Text>
                  <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 1 }}>
                    Email: {user.email}
                  </Text>
                  {user.phone && (
                    <Text style={{ fontSize: 12, color: '#6b7280', marginBottom: 1 }}>
                      Mobile: {user.phone}
                    </Text>
                  )}
                  <Text style={{ fontSize: 12, color: '#6b7280' }}>
                    Dept: {user.department || 'Not specified'}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>


    </View>
  );
};

export default DirectoryScreen;
