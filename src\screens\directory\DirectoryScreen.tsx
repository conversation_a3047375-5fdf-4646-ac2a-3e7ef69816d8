import React, { useEffect, useState } from 'react';
import { View, ScrollView, Image, Linking, TouchableOpacity } from 'react-native';
import { Text, ActivityIndicator, List, TextInput, Menu, Button, Card, Portal, Dialog, Divider, IconButton } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  phone?: string;
  photoURL?: string;
}

const DirectoryScreen = ({ navigation }: any) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [deptMenuVisible, setDeptMenuVisible] = useState(false);
  const [roleMenuVisible, setRoleMenuVisible] = useState(false);
  const [currentUserRole, setCurrentUserRole] = useState<string>('employee');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [profileDialogVisible, setProfileDialogVisible] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;

      // Get current user's role
      const userRole = currentUser.role || 'employee';
      setCurrentUserRole(userRole);

      // Get all users from Supabase
      const usersData = await db.users.getAll();
      const usersList: User[] = usersData.map((userData: any) => ({
        id: userData.id,
        name: userData.name || '',
        email: userData.email || '',
        role: userData.role || 'employee',
        department: userData.department || '',
        phone: userData.phone || '',
        photoURL: userData.photo_url || '',
      }));

      // Sort users by name
      usersList.sort((a, b) => a.name.localeCompare(b.name));
      setUsers(usersList);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get unique departments for filter
  const departments = Array.from(new Set(users.map(u => u.department || '').filter(Boolean)));
  
  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const searchMatch = 
      user.name.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase()) ||
      (user.department && user.department.toLowerCase().includes(search.toLowerCase()));
    
    const deptMatch = departmentFilter === 'all' || user.department === departmentFilter;
    const roleMatch = roleFilter === 'all' || user.role === roleFilter;
    
    return searchMatch && deptMatch && roleMatch;
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin': return '#dc2626';
      case 'manager': return '#f59e0b';
      case 'employee': return '#1e3a8a';
      default: return '#666';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superadmin': return 'shield-crown';
      case 'manager': return 'account-tie';
      case 'employee': return 'account';
      default: return 'account';
    }
  };

  const handleUserPress = (user: User) => {
    setSelectedUser(user);
    setProfileDialogVisible(true);
  };

  const handleEmailPress = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  const handlePhonePress = (phone: string) => {
    Linking.openURL(`tel:${phone}`);
  };

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 16 }}>Loading directory...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <View style={{ padding: 16 }}>
        {/* Header */}
        <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 16, color: '#1e3a8a' }}>
          Employee Directory
        </Text>
        
        {/* Search and Filters */}
        <TextInput
          placeholder="Search by name, email, or department"
          value={search}
          onChangeText={setSearch}
          style={{ marginBottom: 12 }}
          left={<TextInput.Icon icon="magnify" />}
        />
        
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 }}>
          <Menu
            visible={deptMenuVisible}
            onDismiss={() => setDeptMenuVisible(false)}
            anchor={
              <Button mode="outlined" onPress={() => setDeptMenuVisible(true)} style={{ flex: 1, marginRight: 8 }}>
                {departmentFilter === 'all' ? 'All Departments' : departmentFilter}
              </Button>
            }
          >
            <Menu.Item onPress={() => setDepartmentFilter('all')} title="All Departments" />
            {departments.map(dept => (
              <Menu.Item key={dept} onPress={() => setDepartmentFilter(dept)} title={dept} />
            ))}
          </Menu>
          
          <Menu
            visible={roleMenuVisible}
            onDismiss={() => setRoleMenuVisible(false)}
            anchor={
              <Button mode="outlined" onPress={() => setRoleMenuVisible(true)} style={{ flex: 1, marginLeft: 8 }}>
                {roleFilter === 'all' ? 'All Roles' : roleFilter.charAt(0).toUpperCase() + roleFilter.slice(1)}
              </Button>
            }
          >
            <Menu.Item onPress={() => setRoleFilter('all')} title="All Roles" />
            <Menu.Item onPress={() => setRoleFilter('employee')} title="Employee" />
            <Menu.Item onPress={() => setRoleFilter('manager')} title="Manager" />
            <Menu.Item onPress={() => setRoleFilter('superadmin')} title="Superadmin" />
          </Menu>
        </View>

        {/* Results count */}
        <Text style={{ color: '#666', marginBottom: 16 }}>
          {filteredUsers.length} {filteredUsers.length === 1 ? 'person' : 'people'} found
        </Text>
      </View>

      {/* User List */}
      <ScrollView style={{ flex: 1 }}>
        {filteredUsers.length === 0 ? (
          <View style={{ padding: 32, alignItems: 'center' }}>
            <MaterialCommunityIcons name="account-search" size={64} color="#ccc" />
            <Text style={{ color: '#666', marginTop: 16, textAlign: 'center' }}>
              No users found matching your search criteria
            </Text>
          </View>
        ) : (
          filteredUsers.map((user) => (
            <TouchableOpacity key={user.id} onPress={() => handleUserPress(user)}>
              <Card style={{ margin: 8, marginHorizontal: 16 }}>
                <List.Item
                  title={user.name || user.email}
                  description={`${user.email}${user.department ? ' • ' + user.department : ''}`}
                left={() => (
                  <View style={{ marginRight: 12, alignItems: 'center', justifyContent: 'center' }}>
                    {user.photoURL ? (
                      <Image
                        source={{ uri: user.photoURL }}
                        style={{ width: 48, height: 48, borderRadius: 24 }}
                      />
                    ) : (
                      <View style={{
                        width: 48,
                        height: 48,
                        borderRadius: 24,
                        backgroundColor: getRoleColor(user.role),
                        justifyContent: 'center',
                        alignItems: 'center'
                      }}>
                        <MaterialCommunityIcons 
                          name={getRoleIcon(user.role)} 
                          size={24} 
                          color="white" 
                        />
                      </View>
                    )}
                  </View>
                )}
                right={() => (
                  <View style={{ justifyContent: 'center', alignItems: 'flex-end' }}>
                    <Text style={{ 
                      color: getRoleColor(user.role), 
                      fontWeight: 'bold',
                      fontSize: 12,
                      textTransform: 'uppercase'
                    }}>
                      {user.role}
                    </Text>
                    {user.phone && (
                      <Text style={{ color: '#666', fontSize: 12, marginTop: 2 }}>
                        {user.phone}
                      </Text>
                    )}
                  </View>
                )}
                />
              </Card>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>

      {/* Profile Detail Dialog */}
      <Portal>
        <Dialog
          visible={profileDialogVisible}
          onDismiss={() => setProfileDialogVisible(false)}
          style={{ maxHeight: '80%' }}
        >
          <Dialog.Title style={{ textAlign: 'center', paddingBottom: 0 }}>
            Profile Details
          </Dialog.Title>

          {selectedUser && (
            <Dialog.Content>
              <ScrollView showsVerticalScrollIndicator={false}>
                {/* Profile Photo and Name */}
                <View style={{ alignItems: 'center', marginBottom: 24 }}>
                  {selectedUser.photoURL ? (
                    <Image
                      source={{ uri: selectedUser.photoURL }}
                      style={{ width: 100, height: 100, borderRadius: 50, marginBottom: 16 }}
                    />
                  ) : (
                    <View style={{
                      width: 100,
                      height: 100,
                      borderRadius: 50,
                      backgroundColor: getRoleColor(selectedUser.role),
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: 16
                    }}>
                      <MaterialCommunityIcons
                        name={getRoleIcon(selectedUser.role)}
                        size={48}
                        color="white"
                      />
                    </View>
                  )}

                  <Text style={{ fontSize: 24, fontWeight: 'bold', textAlign: 'center' }}>
                    {selectedUser.name || 'No Name'}
                  </Text>

                  <Text style={{
                    fontSize: 16,
                    color: getRoleColor(selectedUser.role),
                    fontWeight: 'bold',
                    textTransform: 'uppercase',
                    marginTop: 4
                  }}>
                    {selectedUser.role}
                  </Text>
                </View>

                <Divider style={{ marginBottom: 16 }} />

                {/* Contact Information */}
                <View style={{ marginBottom: 16 }}>
                  <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 12, color: '#1e3a8a' }}>
                    Contact Information
                  </Text>

                  {/* Email */}
                  <TouchableOpacity
                    onPress={() => handleEmailPress(selectedUser.email)}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 8,
                      paddingHorizontal: 12,
                      backgroundColor: '#f8fafc',
                      borderRadius: 8,
                      marginBottom: 8
                    }}
                  >
                    <MaterialCommunityIcons name="email" size={20} color="#1e3a8a" />
                    <Text style={{ marginLeft: 12, flex: 1, color: '#1e3a8a' }}>
                      {selectedUser.email}
                    </Text>
                    <MaterialCommunityIcons name="open-in-new" size={16} color="#666" />
                  </TouchableOpacity>

                  {/* Phone */}
                  {selectedUser.phone && (
                    <TouchableOpacity
                      onPress={() => handlePhonePress(selectedUser.phone!)}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: 8,
                        paddingHorizontal: 12,
                        backgroundColor: '#f8fafc',
                        borderRadius: 8,
                        marginBottom: 8
                      }}
                    >
                      <MaterialCommunityIcons name="phone" size={20} color="#1e3a8a" />
                      <Text style={{ marginLeft: 12, flex: 1, color: '#1e3a8a' }}>
                        {selectedUser.phone}
                      </Text>
                      <MaterialCommunityIcons name="open-in-new" size={16} color="#666" />
                    </TouchableOpacity>
                  )}
                </View>

                {/* Department Information */}
                {selectedUser.department && (
                  <View style={{ marginBottom: 16 }}>
                    <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 12, color: '#1e3a8a' }}>
                      Department
                    </Text>
                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingVertical: 8,
                      paddingHorizontal: 12,
                      backgroundColor: '#f8fafc',
                      borderRadius: 8
                    }}>
                      <MaterialCommunityIcons name="office-building" size={20} color="#1e3a8a" />
                      <Text style={{ marginLeft: 12, fontSize: 16 }}>
                        {selectedUser.department}
                      </Text>
                    </View>
                  </View>
                )}

                {/* Role Information */}
                <View style={{ marginBottom: 16 }}>
                  <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 12, color: '#1e3a8a' }}>
                    Role & Permissions
                  </Text>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 8,
                    paddingHorizontal: 12,
                    backgroundColor: '#f8fafc',
                    borderRadius: 8
                  }}>
                    <MaterialCommunityIcons
                      name={getRoleIcon(selectedUser.role)}
                      size={20}
                      color={getRoleColor(selectedUser.role)}
                    />
                    <View style={{ marginLeft: 12, flex: 1 }}>
                      <Text style={{ fontSize: 16, fontWeight: 'bold', color: getRoleColor(selectedUser.role) }}>
                        {selectedUser.role.charAt(0).toUpperCase() + selectedUser.role.slice(1)}
                      </Text>
                      <Text style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                        {selectedUser.role === 'superadmin' && 'Full system access and user management'}
                        {selectedUser.role === 'manager' && 'Team management and approval permissions'}
                        {selectedUser.role === 'employee' && 'Standard user access'}
                      </Text>
                    </View>
                  </View>
                </View>
              </ScrollView>
            </Dialog.Content>
          )}

          <Dialog.Actions>
            <Button onPress={() => setProfileDialogVisible(false)}>Close</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

export default DirectoryScreen;
