import React, { useEffect, useState } from 'react';
import { View, Image, Platform } from 'react-native';
import { Text, Button, TextInput, ActivityIndicator, Card, Menu } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import * as ImagePicker from 'expo-image-picker';
import { DEPARTMENTS } from '../../utils/departments';

interface UserProfile {
  name: string;
  email: string;
  phone?: string;
  department?: string;
  photoURL?: string;
}

const EditProfileScreen = ({ navigation }: any) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [department, setDepartment] = useState('');
  const [photoURL, setPhotoURL] = useState('');
  const [departmentMenuVisible, setDepartmentMenuVisible] = useState(false);



  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;

      try {
        const userData = await db.users.getById(currentUser.id);
        if (userData) {
          const data: UserProfile = {
            name: userData.name,
            email: userData.email,
            phone: userData.phone,
            department: userData.department,
            photoURL: userData.photo_url,
          };
          setProfile(data);
          setName(data.name || '');
          setPhone(data.phone || '');
          setDepartment(data.department || '');
          setPhotoURL(data.photoURL || '');
        } else {
          // User doesn't exist yet, initialize with current user info
          setProfile({ name: '', email: currentUser.email });
          setName('');
          setPhone('');
          setDepartment('');
          setPhotoURL('');
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
        // Initialize with empty values if there's an error
        setProfile({ name: '', email: currentUser.email });
        setName('');
        setPhone('');
        setDepartment('');
        setPhotoURL('');
      }

      setLoading(false);
    };
    fetchProfile();
  }, []);

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.5,
    });
    if (!result.canceled && result.assets && result.assets.length > 0) {
      setPhotoURL(result.assets[0].uri);
    }
  };



  const handleSave = async () => {
    setSaving(true);
    setError('');
    try {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) throw new Error('Not authenticated');

      console.log('Saving profile for user:', currentUser.id);
      console.log('Profile data:', { name, email: currentUser.email, phone, department, photoURL });

      // Update user profile in Supabase
      await db.users.update(currentUser.id, {
        name,
        phone,
        department,
        photo_url: photoURL,
      });

      console.log('Profile saved successfully');
      navigation.goBack();
    } catch (e: any) {
      console.error('Error saving profile:', e);
      setError(e.message);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator />
      </View>
    );
  }

  return (
    <View style={{ flex: 1, padding: 16, alignItems: 'center' }}>
      <Card style={{ width: '100%', maxWidth: 400, padding: 16 }}>
        <Button mode="outlined" onPress={pickImage} style={{ marginBottom: 16 }}>
          {photoURL ? 'Change Photo' : 'Add Photo'}
        </Button>
        {photoURL ? (
          <Image source={{ uri: photoURL }} style={{ width: 100, height: 100, borderRadius: 50, alignSelf: 'center', marginBottom: 16 }} />
        ) : null}
        <TextInput
          label="Name"
          value={name}
          onChangeText={setName}
          style={{ marginBottom: 8 }}
        />
        <TextInput
          label="Phone"
          value={phone}
          onChangeText={setPhone}
          keyboardType="phone-pad"
          style={{ marginBottom: 8 }}
        />
        {/* Department Dropdown */}
        <View style={{ marginBottom: 8 }}>
          <Text style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>Department</Text>
          <Menu
            visible={departmentMenuVisible}
            onDismiss={() => setDepartmentMenuVisible(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setDepartmentMenuVisible(true)}
                style={{ justifyContent: 'flex-start' }}
              >
                {department || 'Select Department'}
              </Button>
            }
          >
            {DEPARTMENTS.map((dept) => (
              <Menu.Item
                key={dept}
                onPress={() => {
                  setDepartment(dept);
                  setDepartmentMenuVisible(false);
                }}
                title={dept}
              />
            ))}
          </Menu>
        </View>
        {error ? <Text style={{ color: 'red', marginBottom: 8 }}>{error}</Text> : null}
        <Button mode="contained" onPress={handleSave} loading={saving} disabled={saving} style={{ marginTop: 16 }}>
          Save
        </Button>
        <Button onPress={() => navigation.goBack()} style={{ marginTop: 8 }}>
          Cancel
        </Button>
      </Card>
    </View>
  );
};

export default EditProfileScreen; 