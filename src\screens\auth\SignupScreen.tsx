import React, { useState } from 'react';
import { View, Image, ScrollView, StyleSheet, Dimensions, StatusBar } from 'react-native';
import { Text, TextInput, Button, IconButton } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

const { width, height } = Dimensions.get('window');

type AuthStackParamList = {
  Login: undefined;
  Signup: undefined;
};

type Props = NativeStackScreenProps<AuthStackParamList, 'Signup'>;

const SignupScreen: React.FC<Props> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleSignup = async () => {
    setLoading(true);
    setError('');
    try {
      // Use Supabase Auth for signup - this will automatically create the user profile
      const user = await supabaseAuthService.signUp(email, password, name);
      console.log('User signed up successfully:', user);

      // Navigation will be handled by auth state change
    } catch (e: any) {
      console.error('Signup error:', e);
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header with Logo and Icons */}
      <View style={styles.header}>
        <Image
          source={require('../../../assets/texam-logo.png')}
          style={styles.headerLogo}
          resizeMode="contain"
        />
        <View style={styles.headerIcons}>
          <IconButton
            icon="bell-outline"
            size={24}
            iconColor="#666"
            onPress={() => {/* Handle notifications */}}
          />
          <IconButton
            icon="account-circle-outline"
            size={24}
            iconColor="#666"
            onPress={() => {/* Handle profile */}}
          />
        </View>
      </View>


      <ScrollView contentContainerStyle={styles.scrollView}>
        <View style={styles.content}>
          <Text style={styles.title}>Create Your Account</Text>

          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Register to Sign Up</Text>
            {error ? <Text style={styles.errorText}>{error}</Text> : null}

            <TextInput
              label="Name"
              value={name}
              onChangeText={setName}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="account-outline" />}
              outlineColor="#e0e0e0"
              activeOutlineColor="#00a9e9"
            />
            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
              left={<TextInput.Icon icon="email-outline" />}
              outlineColor="#e0e0e0"
              activeOutlineColor="#00a9e9"
            />
            <TextInput
              label="Password"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              secureTextEntry={!showPassword}
              style={styles.input}
              left={<TextInput.Icon icon="lock-outline" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? "eye-off-outline" : "eye-outline"}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
              outlineColor="#e0e0e0"
              activeOutlineColor="#00a9e9"
            />

            <Button
              mode="contained"
              loading={loading}
              onPress={handleSignup}
              style={styles.signInButton}
            >
              <View style={styles.signInButtonContent}>
                <Text style={styles.signInButtonText}>Register</Text>
              </View>
            </Button>

            <View style={styles.signUpContainer}>
              <Text style={styles.alreadyHaveAccount}>ALREADY HAVE AN ACCOUNT?</Text>
              <Button
                mode="outlined"
                onPress={() => navigation.navigate('Login')}
                style={styles.createAccountButton}
                labelStyle={styles.createAccountButtonText}
                icon="arrow-right"
              >
                Go to Login
              </Button>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    backgroundColor: '#ffffff',
  },
  headerLogo: {
    height: 40,
    width: width * 0.4,
  },
  headerIcons: {
    flexDirection: 'row',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e3a8a',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  formContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 24,
    padding: 24,
    marginTop: 10,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
  },
  input: {
    marginBottom: 16,
    backgroundColor: '#ffffff',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginBottom: 16,
    textAlign: 'center',
  },
  signInButton: {
    backgroundColor: '#1e397e',
    borderRadius: 12,
    marginBottom: 16,
  },
  signInButtonContent: {
    height: 30,
    flexDirection: 'row-reverse',
  },
  signInButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  resetButton: {
    borderColor: '#00a9e9',
    borderRadius: 12,
    marginBottom: 24,
  },
  resetButtonText: {
    color: '#00a9e9',
    fontSize: 16,
  },
  signUpContainer: {
    alignItems: 'center',
    color: '#666',
  },
  alreadyHaveAccount: {
    fontSize: 12,
    textAlign: 'center',
    color: '#999',
    fontWeight: '600',
    letterSpacing: 1,
    marginBottom: 12,
  },
  createAccountButton: {
    borderColor: '#999',
    borderRadius: 12,
    width: '100%',
  },
  createAccountButtonText: {
    color: '#666',
    fontSize: 16,
  },
});

export default SignupScreen; 