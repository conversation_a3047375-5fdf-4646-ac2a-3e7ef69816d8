import React, { useState } from 'react';
import { View, Image, ScrollView, StyleSheet, Dimensions, StatusBar } from 'react-native';
import { Text, TextInput, Button, IconButton } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

type AuthStackParamList = {
  Login: undefined;
  Signup: undefined;
};

type Props = NativeStackScreenProps<AuthStackParamList, 'Signup'>;

const SignupScreen: React.FC<Props> = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleSignup = async () => {
    setLoading(true);
    setError('');
    try {
      // Use Supabase Auth for signup - this will automatically create the user profile
      const user = await supabaseAuthService.signUp(email, password, name);
      console.log('User signed up successfully:', user);

      // Navigation will be handled by auth state change
    } catch (e: any) {
      console.error('Signup error:', e);
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#f8f9fa" />

      {/* Clean Header with Logo and Icons */}
      <View style={styles.header}>
        <Image
          source={require('../../../assets/texam-logo.png')}
          style={styles.headerLogo}
          resizeMode="contain"
        />
        <View style={styles.headerIcons}>
          <View style={styles.notificationContainer}>
            <MaterialCommunityIcons name="bell-outline" size={20} color="#666" />
            <View style={styles.notificationBadge}>
              <Text style={styles.notificationText}>2</Text>
            </View>
          </View>
          <View style={styles.profileAvatar}>
            <MaterialCommunityIcons name="account" size={20} color="#fff" />
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Centered Content */}
        <View style={styles.content}>
          {/* Main Title */}
          <Text style={styles.mainTitle}>TEXAM WorkFlow Management</Text>
          <Text style={styles.subtitle}>
            Comprehensive WorkFlow Suite for managing Employee Directory,
            Announcements, Project Updates, Task Management, & Leave Requests.
          </Text>

          {/* Sign Up Form Card */}
          <View style={styles.formCard}>
            <Text style={styles.formTitle}>Create Your Account</Text>

            {error ? <Text style={styles.errorText}>{error}</Text> : null}

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Full Name</Text>
              <TextInput
                placeholder="Enter your full name"
                value={name}
                onChangeText={setName}
                mode="outlined"
                style={styles.input}
                outlineStyle={styles.inputOutline}
                contentStyle={styles.inputContent}
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                placeholder="Enter your email"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                style={styles.input}
                outlineStyle={styles.inputOutline}
                contentStyle={styles.inputContent}
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Password</Text>
              <TextInput
                placeholder="Enter your password"
                value={password}
                onChangeText={setPassword}
                mode="outlined"
                secureTextEntry={!showPassword}
                style={styles.input}
                outlineStyle={styles.inputOutline}
                contentStyle={styles.inputContent}
                placeholderTextColor="#999"
                right={
                  <TextInput.Icon
                    icon={showPassword ? "eye-off-outline" : "eye-outline"}
                    onPress={() => setShowPassword(!showPassword)}
                    iconColor="#999"
                  />
                }
              />
            </View>

            <Button
              mode="contained"
              onPress={handleSignup}
              loading={loading}
              disabled={loading}
              style={styles.signInButton}
              labelStyle={styles.signInButtonText}
              icon="arrow-right"
              contentStyle={styles.signInButtonContent}
            >
              Create Account
            </Button>

            <View style={styles.divider}>
              <Text style={styles.alreadyHaveAccount}>ALREADY HAVE AN ACCOUNT?</Text>
            </View>

            <Button
              mode="outlined"
              onPress={() => navigation.navigate('Login')}
              style={styles.createAccountButton}
              labelStyle={styles.createAccountButtonText}
              icon="arrow-right"
              contentStyle={styles.createAccountButtonContent}
            >
              Sign In
            </Button>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: '#f8f9fa',
  },
  headerLogo: {
    height: 32,
    width: width * 0.35,
  },
  headerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  notificationContainer: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#ef4444',
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  profileAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#1e3a8a',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 40,
    alignItems: 'center',
  },
  mainTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 13,
    color: '#666',
    textAlign: 'center',
    lineHeight: 18,
    marginBottom: 60,
    paddingHorizontal: 20,
  },
  formCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 32,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    textAlign: 'center',
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#ffffff',
    fontSize: 14,
  },
  inputOutline: {
    borderColor: '#e5e7eb',
    borderWidth: 1,
    borderRadius: 8,
  },
  inputContent: {
    fontSize: 14,
    color: '#1a1a1a',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginBottom: 16,
    textAlign: 'center',
  },
  signInButton: {
    backgroundColor: '#1e3a8a',
    borderRadius: 8,
    marginBottom: 16,
    marginTop: 8,
  },
  signInButtonContent: {
    height: 48,
    flexDirection: 'row-reverse',
  },
  signInButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  divider: {
    alignItems: 'center',
    marginBottom: 16,
  },
  alreadyHaveAccount: {
    fontSize: 11,
    color: '#999',
    fontWeight: '500',
    letterSpacing: 0.5,
  },
  createAccountButton: {
    borderColor: '#00a9e9',
    borderRadius: 8,
    borderWidth: 1,
  },
  createAccountButtonContent: {
    height: 44,
    flexDirection: 'row-reverse',
  },
  createAccountButtonText: {
    color: '#00a9e9',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default SignupScreen; 