import { supabase } from './supabase';
import { Session, User as SupabaseUser } from '@supabase/supabase-js';

export interface User {
  id: string;
  email: string;
  name?: string;
  role?: string;
  managerId?: string;
}

class SupabaseAuthService {
  private currentUser: User | null = null;
  private listeners: ((user: User | null) => void)[] = [];

  constructor() {
    // Listen to auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Supabase auth event:', event);
      
      if (session?.user) {
        await this.handleUserSession(session.user);
      } else {
        this.currentUser = null;
        this.notifyListeners();
      }
    });
  }

  private async handleUserSession(supabaseUser: SupabaseUser) {
    try {
      // Get user profile from our users table
      const { data: userProfile, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', supabaseUser.id)
        .single();

      let finalUserProfile = userProfile;

      if (error && error.code === 'PGRST116') {
        // User doesn't exist in our users table, create them
        console.log('Trigger may have failed. Creating user profile manually for:', supabaseUser.email);

        // Wait a moment in case the trigger is just slow
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Try fetching again
        const { data: retryProfile, error: retryError } = await supabase
          .from('users')
          .select('*')
          .eq('id', supabaseUser.id)
          .single();

        if (retryProfile) {
          console.log('Profile found on retry - trigger worked!');
          finalUserProfile = retryProfile;
        } else {
          // Create manually as backup
          const { data: newProfile, error: createError } = await supabase
            .from('users')
            .insert({
              id: supabaseUser.id,
              email: supabaseUser.email,
              name: supabaseUser.user_metadata?.name ||
                    supabaseUser.user_metadata?.full_name ||
                    supabaseUser.email?.split('@')[0],
              role: 'employee',
            })
            .select()
            .single();

          if (createError) {
            console.error('Error creating user profile manually:', createError);
            throw new Error(`Failed to create user profile: ${createError.message}`);
          }

          finalUserProfile = newProfile;
          console.log('User profile created manually as backup:', finalUserProfile);
        }
      } else if (error) {
        console.error('Error fetching user profile:', error);
        throw new Error(`Failed to fetch user profile: ${error.message}`);
      }

      // Set current user
      this.currentUser = {
        id: supabaseUser.id,
        email: supabaseUser.email!,
        name: finalUserProfile?.name,
        role: finalUserProfile?.role,
        managerId: finalUserProfile?.manager_id,
      };

      console.log('User session established:', this.currentUser);
      this.notifyListeners();
    } catch (error) {
      console.error('Error handling user session:', error);
      this.currentUser = null;
      this.notifyListeners();
    }
  }

  // Get current user
  getCurrentUser(): User | null {
    if (!this.currentUser) {
      console.warn('No current user found. User may not be logged in or session not initialized.');
    }
    return this.currentUser;
  }

  // Sign up with email and password
  async signUp(email: string, password: string, name?: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name || email.split('@')[0],
            full_name: name || email.split('@')[0],
          },
        },
      });

      if (error) throw error;

      if (!data.user) {
        throw new Error('Failed to create user');
      }

      console.log('User signed up successfully:', data.user.id);

      // If user is immediately confirmed (email confirmations disabled),
      // ensure profile is created
      if (data.user.email_confirmed_at) {
        console.log('User email already confirmed, ensuring profile exists...');
        await this.ensureUserProfile(data.user);
      }

      // The user will be handled by the auth state change listener
      return {
        id: data.user.id,
        email: data.user.email!,
        name: name,
      };
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  // Sign in with email and password
  async signIn(email: string, password: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      if (!data.user) {
        throw new Error('Failed to sign in');
      }

      // The user will be handled by the auth state change listener
      return {
        id: data.user.id,
        email: data.user.email!,
      };
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      this.currentUser = null;
      this.notifyListeners();
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  // Update password
  async updatePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      console.log('Starting password update...');

      // Just update the password directly - Supabase handles current user verification
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (updateError) {
        console.error('Password update error:', updateError);
        throw new Error(updateError.message || 'Failed to update password');
      }

      console.log('Password updated successfully');
    } catch (error) {
      console.error('Update password error:', error);
      throw error;
    }
  }

  // Update user profile
  async updateProfile(updates: Partial<User>): Promise<void> {
    try {
      if (!this.currentUser) {
        throw new Error('No user logged in');
      }

      const { error } = await supabase
        .from('users')
        .update({
          name: updates.name,
          phone: updates.phone,
          department: updates.department,
          photo_url: updates.photoURL,
        })
        .eq('id', this.currentUser.id);

      if (error) throw error;

      // Update current user
      this.currentUser = { ...this.currentUser, ...updates };
      this.notifyListeners();
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  }

  // Listen to auth state changes
  onAuthStateChanged(callback: (user: User | null) => void): () => void {
    this.listeners.push(callback);
    
    // Immediately call with current user
    callback(this.currentUser);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.currentUser));
  }

  // Ensure user profile exists in our users table
  private async ensureUserProfile(supabaseUser: SupabaseUser): Promise<any> {
    try {
      // Check if profile already exists
      const { data: existingProfile, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', supabaseUser.id)
        .single();

      if (existingProfile) {
        console.log('User profile already exists');
        return existingProfile;
      }

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      // Create the profile
      console.log('Creating user profile manually...');
      const { data: newProfile, error: createError } = await supabase
        .from('users')
        .insert({
          id: supabaseUser.id,
          email: supabaseUser.email,
          name: supabaseUser.user_metadata?.name ||
                supabaseUser.user_metadata?.full_name ||
                supabaseUser.email?.split('@')[0],
          role: 'employee',
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating user profile manually:', createError);
        throw createError;
      }

      console.log('User profile created manually:', newProfile);
      return newProfile;
    } catch (error) {
      console.error('Error ensuring user profile:', error);
      throw error;
    }
  }

  // Get current session
  async getSession(): Promise<Session | null> {
    const { data: { session } } = await supabase.auth.getSession();
    return session;
  }

  // Initialize auth service
  async initialize(): Promise<void> {
    try {
      const session = await this.getSession();
      if (session?.user) {
        await this.handleUserSession(session.user);
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
    }
  }
}

export const supabaseAuthService = new SupabaseAuthService();
