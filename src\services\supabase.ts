import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://nmmpfufiiaqspbxrqocn.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5tbXBmdWZpaWFxc3BieHJxb2NuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0OTI0NjgsImV4cCI6MjA2NzA2ODQ2OH0.tdAhPaP3jS4QHVD7uqyvCGTbVZCjpNmf4fPJnNiPhkA';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database helper functions
export const db = {
  // Users
  users: {
    async getAll() {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    },

    async getById(id: string) {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data;
    },

    async create(userData: any) {
      // Remove id from userData if it exists, let Supabase generate it
      const { id, ...userDataWithoutId } = userData;

      const { data, error } = await supabase
        .from('users')
        .insert(userDataWithoutId)
        .select()
        .single();

      if (error) {
        console.error('Supabase user creation error:', error);
        throw error;
      }
      return data;
    },

    async update(id: string, updates: any) {
      try {
        console.log('Updating user:', id, 'with:', updates);

        // First check if user exists
        const { data: existingUser, error: fetchError } = await supabase
          .from('users')
          .select('*')
          .eq('id', id)
          .single();

        if (fetchError) {
          console.error('User not found for update:', fetchError);
          throw new Error(`User not found: ${fetchError.message}`);
        }

        console.log('Found user to update:', existingUser);

        // Perform the update
        const { data, error } = await supabase
          .from('users')
          .update(updates)
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error('Update error:', error);
          throw error;
        }

        console.log('User updated successfully:', data);
        return data;
      } catch (error) {
        console.error('Error in users.update:', error);
        throw error;
      }
    },

    async delete(id: string) {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    }
  },

  // Projects
  projects: {
    async getAll() {
      try {
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Supabase projects getAll error:', error);
          throw error;
        }
        return data || [];
      } catch (error) {
        console.error('Error in projects.getAll:', error);
        throw error;
      }
    },

    async getById(id: string) {
      try {
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          console.error('Supabase projects getById error:', error);
          throw error;
        }
        return data;
      } catch (error) {
        console.error('Error in projects.getById:', error);
        throw error;
      }
    },

    async create(projectData: any) {
      try {
        const { data, error } = await supabase
          .from('projects')
          .insert(projectData)
          .select()
          .single();

        if (error) {
          console.error('Supabase projects create error:', error);
          throw error;
        }
        return data;
      } catch (error) {
        console.error('Error in projects.create:', error);
        throw error;
      }
    },

    async update(id: string, updates: any) {
      try {
        const { data, error } = await supabase
          .from('projects')
          .update(updates)
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error('Supabase projects update error:', error);
          throw error;
        }
        return data;
      } catch (error) {
        console.error('Error in projects.update:', error);
        throw error;
      }
    },

    async delete(id: string) {
      try {
        const { error } = await supabase
          .from('projects')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Supabase projects delete error:', error);
          throw error;
        }
        return true;
      } catch (error) {
        console.error('Error in projects.delete:', error);
        throw error;
      }
    }
  },

  // Project Updates
  projectUpdates: {
    async getAll(filters?: any) {
      try {
        let query = supabase
          .from('project_updates')
          .select('*')
          .order('created_at', { ascending: false });

        if (filters?.projectId) {
          query = query.eq('project_id', filters.projectId);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Supabase project_updates getAll error:', error);
          throw error;
        }
        return data || [];
      } catch (error) {
        console.error('Error in projectUpdates.getAll:', error);
        throw error;
      }
    },

    async getById(id: string) {
      try {
        console.log('Supabase: Fetching project update with ID:', id);
        const { data, error } = await supabase
          .from('project_updates')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          console.error('Supabase project_updates getById error:', error);
          if (error.code === 'PGRST116') {
            console.log('No project update found with ID:', id);
            return null;
          }
          throw error;
        }

        console.log('Supabase: Project update data retrieved:', data);
        return data;
      } catch (error) {
        console.error('Error in projectUpdates.getById:', error);
        throw error;
      }
    },

    async create(updateData: any) {
      try {
        const { data, error } = await supabase
          .from('project_updates')
          .insert(updateData)
          .select()
          .single();

        if (error) {
          console.error('Supabase project_updates create error:', error);
          throw error;
        }
        return data;
      } catch (error) {
        console.error('Error in projectUpdates.create:', error);
        throw error;
      }
    },

    async update(id: string, updates: any) {
      try {
        const { data, error } = await supabase
          .from('project_updates')
          .update(updates)
          .eq('id', id)
          .select()
          .single();

        if (error) {
          console.error('Supabase project_updates update error:', error);
          throw error;
        }
        return data;
      } catch (error) {
        console.error('Error in projectUpdates.update:', error);
        throw error;
      }
    },

    async delete(id: string) {
      try {
        const { error } = await supabase
          .from('project_updates')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Supabase project_updates delete error:', error);
          throw error;
        }
        return true;
      } catch (error) {
        console.error('Error in projectUpdates.delete:', error);
        throw error;
      }
    }
  },

  // Leaves
  leaves: {
    async getAll(filters?: any) {
      let query = supabase
        .from('leaves')
        .select('*')
        .order('created_at', { ascending: false });

      if (filters?.userId) {
        query = query.eq('user_id', filters.userId);
      }
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      return data || [];
    },

    async create(leaveData: any) {
      const { data, error } = await supabase
        .from('leaves')
        .insert(leaveData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async update(id: string, updates: any) {
      const { data, error } = await supabase
        .from('leaves')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },

    async delete(id: string) {
      try {
        const { error } = await supabase
          .from('leaves')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Supabase leaves delete error:', error);
          throw error;
        }
        return true;
      } catch (error) {
        console.error('Error in leaves.delete:', error);
        throw error;
      }
    }
  },

  // Announcements
  announcements: {
    async getAll() {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    },

    async create(announcementData: any) {
      const { data, error } = await supabase
        .from('announcements')
        .insert(announcementData)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },

    async delete(id: string) {
      const { error } = await supabase
        .from('announcements')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    }
  }
};

// Storage helper
export const storage = {
  async uploadImage(file: File | Blob, path: string): Promise<{ url: string; path: string }> {
    const { data, error } = await supabase.storage
      .from('images')
      .upload(path, file);

    if (error) throw error;

    const { data: urlData } = supabase.storage
      .from('images')
      .getPublicUrl(data.path);

    return {
      url: urlData.publicUrl,
      path: data.path
    };
  },

  async deleteImage(path: string): Promise<void> {
    const { error } = await supabase.storage
      .from('images')
      .remove([path]);

    if (error) throw error;
  }
};
