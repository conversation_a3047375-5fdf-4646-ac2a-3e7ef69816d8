import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  List, 
  Chip, 
  ActivityIndicator, 
  Portal, 
  Dialog, 
  TextInput,
  Menu,
  Searchbar,
  FAB,
  IconButton,
  Divider
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { projectUpdatesService } from '../../services/projectUpdatesService';
import { Project, CreateProjectData } from '../../types/projectUpdates';

const ProjectManagementScreen = ({ navigation }: any) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'completed' | 'on-hold' | 'cancelled'>('all');
  const [statusMenuVisible, setStatusMenuVisible] = useState(false);

  // Create Project Dialog
  const [createDialogVisible, setCreateDialogVisible] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newProjectDescription, setNewProjectDescription] = useState('');
  const [newProjectLocation, setNewProjectLocation] = useState('');
  const [createLoading, setCreateLoading] = useState(false);

  // Project Details Modal
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editLocation, setEditLocation] = useState('');
  const [editStatus, setEditStatus] = useState<'active' | 'completed' | 'on-hold' | 'cancelled'>('active');
  const [updateLoading, setUpdateLoading] = useState(false);

  const currentUser = supabaseAuthService.getCurrentUser();

  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    filterProjects();
  }, [projects, searchQuery, statusFilter]);

  const fetchProjects = async () => {
    setLoading(true);
    try {
      const projectsData = await projectUpdatesService.getProjects();
      setProjects(projectsData);
    } catch (error) {
      console.error('Error fetching projects:', error);
      Alert.alert('Error', 'Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const filterProjects = () => {
    let filtered = projects;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(project => project.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.location?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredProjects(filtered);
  };

  const handleCreateProject = async () => {
    if (!newProjectName.trim()) {
      Alert.alert('Error', 'Project name is required');
      return;
    }

    if (!currentUser) {
      Alert.alert('Error', 'No current user found');
      return;
    }

    setCreateLoading(true);
    try {
      const projectData: CreateProjectData = {
        name: newProjectName.trim(),
        description: newProjectDescription.trim() || undefined,
        location: newProjectLocation.trim() || undefined,
        status: 'active',
      };

      await projectUpdatesService.createProject(projectData);
      
      // Reset form
      setNewProjectName('');
      setNewProjectDescription('');
      setNewProjectLocation('');
      setCreateDialogVisible(false);
      
      // Refresh projects
      fetchProjects();
      
      Alert.alert('Success', 'Project created successfully');
    } catch (error) {
      console.error('Error creating project:', error);
      Alert.alert('Error', 'Failed to create project');
    } finally {
      setCreateLoading(false);
    }
  };

  const openProjectDetails = (project: Project) => {
    setSelectedProject(project);
    setEditName(project.name);
    setEditDescription(project.description || '');
    setEditLocation(project.location || '');
    setEditStatus(project.status);
    setEditMode(false);
    setDetailsModalVisible(true);
  };

  const handleUpdateProject = async () => {
    if (!selectedProject || !editName.trim()) {
      Alert.alert('Error', 'Project name is required');
      return;
    }

    setUpdateLoading(true);
    try {
      const updates = {
        name: editName.trim(),
        description: editDescription.trim() || undefined,
        location: editLocation.trim() || undefined,
        status: editStatus,
      };

      await projectUpdatesService.updateProject(selectedProject.id, updates);
      
      setDetailsModalVisible(false);
      setEditMode(false);
      fetchProjects();
      
      Alert.alert('Success', 'Project updated successfully');
    } catch (error) {
      console.error('Error updating project:', error);
      Alert.alert('Error', 'Failed to update project');
    } finally {
      setUpdateLoading(false);
    }
  };

  const handleDeleteProject = async (project: Project) => {
    Alert.alert(
      'Delete Project',
      `Are you sure you want to delete "${project.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await projectUpdatesService.deleteProject(project.id);
              fetchProjects();
              Alert.alert('Success', 'Project deleted successfully');
            } catch (error) {
              console.error('Error deleting project:', error);
              Alert.alert('Error', 'Failed to delete project');
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#16a34a';
      case 'completed': return '#2563eb';
      case 'on-hold': return '#ea580c';
      case 'cancelled': return '#dc2626';
      default: return '#6b7280';
    }
  };



  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading projects...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Project Management</Text>
        <Text style={styles.subtitle}>Manage all projects and their status</Text>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search projects..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        <Menu
          visible={statusMenuVisible}
          onDismiss={() => setStatusMenuVisible(false)}
          anchor={
            <Button
              mode="outlined"
              onPress={() => setStatusMenuVisible(true)}
              icon="filter"
              style={styles.filterButton}
            >
              {statusFilter === 'all' ? 'All Status' : statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
            </Button>
          }
        >
          <Menu.Item onPress={() => { setStatusFilter('all'); setStatusMenuVisible(false); }} title="All Status" />
          <Menu.Item onPress={() => { setStatusFilter('active'); setStatusMenuVisible(false); }} title="Active" />
          <Menu.Item onPress={() => { setStatusFilter('completed'); setStatusMenuVisible(false); }} title="Completed" />
          <Menu.Item onPress={() => { setStatusFilter('on-hold'); setStatusMenuVisible(false); }} title="On Hold" />
          <Menu.Item onPress={() => { setStatusFilter('cancelled'); setStatusMenuVisible(false); }} title="Cancelled" />
        </Menu>
      </View>

      {/* Projects List */}
      <ScrollView style={styles.projectsList}>
        {filteredProjects.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <MaterialCommunityIcons name="folder-open" size={64} color="#ccc" />
              <Text style={styles.emptyTitle}>No Projects Found</Text>
              <Text style={styles.emptySubtitle}>
                {searchQuery || statusFilter !== 'all' 
                  ? 'Try adjusting your search or filter'
                  : 'Create your first project to get started'
                }
              </Text>
            </Card.Content>
          </Card>
        ) : (
          filteredProjects.map((project) => (
            <Card key={project.id} style={styles.projectCard} onPress={() => openProjectDetails(project)}>
              <Card.Content>
                <View style={styles.projectHeader}>
                  <View style={styles.projectInfo}>
                    <Text style={styles.projectName}>{project.name}</Text>
                    {project.location && (
                      <View style={styles.locationContainer}>
                        <MaterialCommunityIcons name="map-marker" size={14} color="#666" />
                        <Text style={styles.locationText}>{project.location}</Text>
                      </View>
                    )}
                  </View>
                  <Chip
                    style={[styles.statusChip, { backgroundColor: getStatusColor(project.status) }]}
                    textStyle={styles.statusChipText}
                  >
                    {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                  </Chip>
                </View>
                
                {project.description && (
                  <Text style={styles.projectDescription} numberOfLines={2}>
                    {project.description}
                  </Text>
                )}
                
                <Text style={styles.projectDate}>
                  Created: {new Date(project.createdAt).toLocaleDateString()}
                </Text>
              </Card.Content>
            </Card>
          ))
        )}
      </ScrollView>

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => setCreateDialogVisible(true)}
        label="New Project"
      />

      {/* Create Project Dialog */}
      <Portal>
        <Dialog visible={createDialogVisible} onDismiss={() => setCreateDialogVisible(false)}>
          <Dialog.Title>Create New Project</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Project Name *"
              value={newProjectName}
              onChangeText={setNewProjectName}
              style={styles.dialogInput}
              mode="outlined"
            />
            <TextInput
              label="Description"
              value={newProjectDescription}
              onChangeText={setNewProjectDescription}
              style={styles.dialogInput}
              mode="outlined"
              multiline
              numberOfLines={3}
            />
            <TextInput
              label="Location"
              value={newProjectLocation}
              onChangeText={setNewProjectLocation}
              style={styles.dialogInput}
              mode="outlined"
              placeholder="e.g., New York, Building A"
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setCreateDialogVisible(false)} disabled={createLoading}>
              Cancel
            </Button>
            <Button onPress={handleCreateProject} loading={createLoading} disabled={createLoading}>
              Create
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Project Details Modal */}
      <Portal>
        <Dialog
          visible={detailsModalVisible}
          onDismiss={() => setDetailsModalVisible(false)}
          style={styles.detailsModal}
        >
          <Dialog.Title>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editMode ? 'Edit Project' : 'Project Details'}
              </Text>
              <View style={styles.modalActions}>
                {!editMode && (
                  <>
                    <IconButton
                      icon="pencil"
                      size={20}
                      onPress={() => setEditMode(true)}
                    />
                    <IconButton
                      icon="delete"
                      size={20}
                      iconColor="#dc2626"
                      onPress={() => selectedProject && handleDeleteProject(selectedProject)}
                    />
                  </>
                )}
              </View>
            </View>
          </Dialog.Title>

          <Dialog.Content>
            {selectedProject && (
              <ScrollView style={styles.modalContent}>
                {editMode ? (
                  <>
                    <TextInput
                      label="Project Name *"
                      value={editName}
                      onChangeText={setEditName}
                      style={styles.dialogInput}
                      mode="outlined"
                    />
                    <TextInput
                      label="Description"
                      value={editDescription}
                      onChangeText={setEditDescription}
                      style={styles.dialogInput}
                      mode="outlined"
                      multiline
                      numberOfLines={3}
                    />
                    <TextInput
                      label="Location"
                      value={editLocation}
                      onChangeText={setEditLocation}
                      style={styles.dialogInput}
                      mode="outlined"
                    />

                    <Text style={styles.inputLabel}>Status</Text>
                    <View style={styles.statusButtons}>
                      {(['active', 'completed', 'on-hold', 'cancelled'] as const).map((status) => (
                        <Button
                          key={status}
                          mode={editStatus === status ? 'contained' : 'outlined'}
                          onPress={() => setEditStatus(status)}
                          style={[
                            styles.statusButton,
                            editStatus === status && { backgroundColor: getStatusColor(status) }
                          ]}
                          labelStyle={styles.statusButtonLabel}
                        >
                          {status.charAt(0).toUpperCase() + status.slice(1)}
                        </Button>
                      ))}
                    </View>
                  </>
                ) : (
                  <>
                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Name:</Text>
                      <Text style={styles.detailValue}>{selectedProject.name}</Text>
                    </View>

                    {selectedProject.description && (
                      <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Description:</Text>
                        <Text style={styles.detailValue}>{selectedProject.description}</Text>
                      </View>
                    )}

                    {selectedProject.location && (
                      <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>Location:</Text>
                        <Text style={styles.detailValue}>{selectedProject.location}</Text>
                      </View>
                    )}

                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Status:</Text>
                      <Chip
                        style={[styles.statusChip, { backgroundColor: getStatusColor(selectedProject.status) }]}
                        textStyle={styles.statusChipText}
                      >
                        {selectedProject.status.charAt(0).toUpperCase() + selectedProject.status.slice(1)}
                      </Chip>
                    </View>

                    <View style={styles.detailRow}>
                      <Text style={styles.detailLabel}>Created:</Text>
                      <Text style={styles.detailValue}>
                        {new Date(selectedProject.createdAt).toLocaleDateString()}
                      </Text>
                    </View>
                  </>
                )}
              </ScrollView>
            )}
          </Dialog.Content>

          <Dialog.Actions>
            {editMode ? (
              <>
                <Button onPress={() => setEditMode(false)} disabled={updateLoading}>
                  Cancel
                </Button>
                <Button onPress={handleUpdateProject} loading={updateLoading} disabled={updateLoading}>
                  Save Changes
                </Button>
              </>
            ) : (
              <Button onPress={() => setDetailsModalVisible(false)}>
                Close
              </Button>
            )}
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e3a8a',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  searchbar: {
    flex: 1,
  },
  filterButton: {
    minWidth: 120,
  },
  projectsList: {
    flex: 1,
    padding: 16,
  },
  emptyCard: {
    marginTop: 40,
  },
  emptyContent: {
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#666',
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 8,
  },
  projectCard: {
    marginBottom: 12,
    elevation: 2,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  projectInfo: {
    flex: 1,
    marginRight: 12,
  },
  projectName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e3a8a',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  locationText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  statusChip: {
    height: 32,
    paddingHorizontal: 12,
  },
  statusChipText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    lineHeight: 16,
  },
  projectDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  projectDate: {
    fontSize: 12,
    color: '#999',
  },
  fab: {
    margin: 16,
    right: 0,
    bottom: 24,
    backgroundColor: '#dbf5ff',
  },
  dialogInput: {
    marginBottom: 12,
  },
  detailsModal: {
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalActions: {
    flexDirection: 'row',
  },
  modalContent: {
    maxHeight: 400,
  },
  detailRow: {
    marginBottom: 16,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 8,
    marginTop: 8,
  },
  statusButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  statusButton: {
    minWidth: 80,
  },
  statusButtonLabel: {
    fontSize: 12,
  },
});

export default ProjectManagementScreen;
