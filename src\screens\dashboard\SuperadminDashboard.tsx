import React, { useEffect, useState } from 'react';
import { View, ScrollView, Image } from 'react-native';
import { Text, Card, ActivityIndicator } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db, supabase } from '../../services/supabase';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  phone: string;
  photoURL: string;
}

const LOGO = require('../../../assets/texam-logo.png');

const SuperadminDashboard = ({ navigation }: any) => {
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalProjects: 0,
    pendingLeaves: 0,
    recentAnnouncements: 0,
    activeEmployees: 0,
    activeManagers: 0,
  });

  const fetchStats = async () => {
    setLoading(true);
    try {
      // Fetch users data
      const usersData = await db.users.getAll();
      const totalUsers = usersData.length;
      const activeEmployees = usersData.filter((user: any) => user.role === 'employee').length;
      const activeManagers = usersData.filter((user: any) => user.role === 'manager').length;

      // Fetch projects data (if available)
      let totalProjects = 0;
      try {
        const { data: projectsData } = await supabase.from('projects').select('id');
        totalProjects = projectsData?.length || 0;
      } catch (error) {
        console.log('Projects table not available yet');
      }

      // Fetch pending leaves (if available)
      let pendingLeaves = 0;
      try {
        const { data: leavesData } = await supabase.from('leave_applications').select('id').eq('status', 'pending');
        pendingLeaves = leavesData?.length || 0;
      } catch (error) {
        console.log('Leave applications table not available yet');
      }

      // Fetch recent announcements (if available)
      let recentAnnouncements = 0;
      try {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        const { data: announcementsData } = await supabase
          .from('announcements')
          .select('id')
          .gte('created_at', oneWeekAgo.toISOString());
        recentAnnouncements = announcementsData?.length || 0;
      } catch (error) {
        console.log('Announcements table not available yet');
      }

      setStats({
        totalUsers,
        totalProjects,
        pendingLeaves,
        recentAnnouncements,
        activeEmployees,
        activeManagers,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  useEffect(() => {
    const fetchProfile = async () => {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;
      try {
        const userData = await db.users.getById(currentUser.id);
        if (userData) {
          setProfile({
            id: userData.id,
            name: userData.name || '',
            email: userData.email || '',
            role: userData.role || '',
            department: userData.department || '',
            phone: userData.phone || '',
            photoURL: userData.photoURL || '',
          });
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };
    fetchProfile();
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 16 }}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f8f9fa' }}>
      <ScrollView style={{ width: '100%' }} contentContainerStyle={{ paddingBottom: 24, paddingTop: 16 }}>
        <View style={{ paddingHorizontal: 16 }}>

          {/* Welcome Header */}
          <View style={{ marginBottom: 20 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#1a1a1a', marginBottom: 4 }}>
              Good Morning, {profile?.name?.split(' ')[0] || 'Admin'}!
            </Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{ width: 8, height: 8, borderRadius: 4, backgroundColor: '#10b981', marginRight: 6 }} />
              <Text style={{ fontSize: 12, color: '#666', marginRight: 16 }}>System Online</Text>
              <MaterialCommunityIcons name="clock-outline" size={14} color="#666" style={{ marginRight: 4 }} />
              <Text style={{ fontSize: 12, color: '#666' }}>
                {new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
              </Text>
            </View>
          </View>

          {/* Total Users */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#1e3a8a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="account-group" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.totalUsers}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Total Users</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '95%', backgroundColor: '#1e3a8a', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Total Projects */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#16a34a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="folder-multiple" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.totalProjects}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Total Projects</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Progress</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '70%', backgroundColor: '#16a34a', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Pending Leaves */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#ea580c',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="calendar-clock" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.pendingLeaves}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Pending Leaves</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Awaiting</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '30%', backgroundColor: '#ea580c', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Recent Announcements */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#dc2626',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="bullhorn" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.recentAnnouncements}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Recent Announcements</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '60%', backgroundColor: '#dc2626', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Employees */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#059669',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="account" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.activeEmployees}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Employees</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '85%', backgroundColor: '#059669', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Managers */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#7c3aed',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="account-tie" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.activeManagers}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Managers</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '75%', backgroundColor: '#7c3aed', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Admin Panel */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#1e3a8a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="cog" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>Admin Panel</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>Manage Users, Roles and System Settings</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>

          {/* Project Management */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#16a34a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="briefcase-outline" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>Project Management</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>Oversee All Projects and Progress</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>

          {/* System Analytics */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#ea580c',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="chart-line" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>System Analytics</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>View Reports and Performance Metrics</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>


        </View>
      </ScrollView>
    </View>
  );
};

export default SuperadminDashboard;
