import React, { useEffect, useState } from 'react';
import { View, ScrollView, Image } from 'react-native';
import { Text, Card, ActivityIndicator } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db, supabase } from '../../services/supabase';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  phone: string;
  photoURL: string;
}

const LOGO = require('../../../assets/texam-logo.png');

const SuperadminDashboard = ({ navigation }: any) => {
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalProjects: 0,
    pendingLeaves: 0,
    recentAnnouncements: 0,
    activeEmployees: 0,
    activeManagers: 0,
  });

  const fetchStats = async () => {
    setLoading(true);
    try {
      // Fetch users data
      const usersData = await db.users.getAll();
      const totalUsers = usersData.length;
      const activeEmployees = usersData.filter((user: any) => user.role === 'employee').length;
      const activeManagers = usersData.filter((user: any) => user.role === 'manager').length;

      // Fetch projects data (if available)
      let totalProjects = 0;
      try {
        const { data: projectsData } = await supabase.from('projects').select('id');
        totalProjects = projectsData?.length || 0;
      } catch (error) {
        console.log('Projects table not available yet');
      }

      // Fetch pending leaves (if available)
      let pendingLeaves = 0;
      try {
        const { data: leavesData } = await supabase.from('leave_applications').select('id').eq('status', 'pending');
        pendingLeaves = leavesData?.length || 0;
      } catch (error) {
        console.log('Leave applications table not available yet');
      }

      // Fetch recent announcements (if available)
      let recentAnnouncements = 0;
      try {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        const { data: announcementsData } = await supabase
          .from('announcements')
          .select('id')
          .gte('created_at', oneWeekAgo.toISOString());
        recentAnnouncements = announcementsData?.length || 0;
      } catch (error) {
        console.log('Announcements table not available yet');
      }

      setStats({
        totalUsers,
        totalProjects,
        pendingLeaves,
        recentAnnouncements,
        activeEmployees,
        activeManagers,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  useEffect(() => {
    const fetchProfile = async () => {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;
      try {
        const userData = await db.users.getById(currentUser.id);
        if (userData) {
          setProfile({
            id: userData.id,
            name: userData.name || '',
            email: userData.email || '',
            role: userData.role || '',
            department: userData.department || '',
            phone: userData.phone || '',
            photoURL: userData.photoURL || '',
          });
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };
    fetchProfile();
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 16 }}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {/* Header */}
      <View style={{
        backgroundColor: '#fff',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0'
      }}>
        <Text style={{
          fontSize: 24,
          fontWeight: 'bold',
          color: '#1e3a8a',
          textAlign: 'center'
        }}>
          Super Admin Dashboard
        </Text>
      </View>

      {/* Overview Stats */}
      <View style={{ padding: 16 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16, color: '#333' }}>
          System Overview
        </Text>
        
        {/* First Row */}
        <View style={{ flexDirection: 'row', marginBottom: 12 }}>
          <Card style={{ flex: 1, marginRight: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="account-group" size={32} color="#1e3a8a" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1e3a8a', marginTop: 8 }}>
                {stats.totalUsers}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Total Users
              </Text>
            </Card.Content>
          </Card>
          
          <Card style={{ flex: 1, marginLeft: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="folder-multiple" size={32} color="#16a34a" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#16a34a', marginTop: 8 }}>
                {stats.totalProjects}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Total Projects
              </Text>
            </Card.Content>
          </Card>
        </View>

        {/* Second Row */}
        <View style={{ flexDirection: 'row', marginBottom: 12 }}>
          <Card style={{ flex: 1, marginRight: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="calendar-clock" size={32} color="#ea580c" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ea580c', marginTop: 8 }}>
                {stats.pendingLeaves}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Pending Leaves
              </Text>
            </Card.Content>
          </Card>
          
          <Card style={{ flex: 1, marginLeft: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="bullhorn" size={32} color="#dc2626" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#dc2626', marginTop: 8 }}>
                {stats.recentAnnouncements}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Recent Announcements
              </Text>
            </Card.Content>
          </Card>
        </View>

        {/* Third Row */}
        <View style={{ flexDirection: 'row', marginBottom: 16 }}>
          <Card style={{ flex: 1, marginRight: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="account" size={32} color="#059669" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#059669', marginTop: 8 }}>
                {stats.activeEmployees}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Employees
              </Text>
            </Card.Content>
          </Card>
          
          <Card style={{ flex: 1, marginLeft: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="account-tie" size={32} color="#7c3aed" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#7c3aed', marginTop: 8 }}>
                {stats.activeManagers}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Managers
              </Text>
            </Card.Content>
          </Card>
        </View>


      </View>
    </ScrollView>
  );
};

export default SuperadminDashboard;
