# Complete Expo React Native App Implementation Guide

## Overview
This guide provides step-by-step instructions to create a fully functional Employee Management App with Leave Management, Directory, Announcements, and Admin Panel using Expo React Native.

## Prerequisites
- Node.js (v16 or higher)
- Expo CLI installed globally: `npm install -g @expo/cli`
- Firebase project setup
- Expo Go app on mobile device for testing

## Step 1: Project Initialization

### 1.1 Create New Expo Project
```bash
npx create-expo-app --template blank-typescript your-app-name
cd your-app-name
```

### 1.2 Install Required Dependencies
```bash
# Core Navigation
npm install @react-navigation/native @react-navigation/native-stack @react-navigation/drawer

# Expo Dependencies
npx expo install react-native-screens react-native-safe-area-context react-native-gesture-handler react-native-reanimated

# UI Library
npm install react-native-paper react-native-vector-icons

# Firebase
npx expo install firebase@9.23.0

# Additional Dependencies
npx expo install @react-native-async-storage/async-storage
npx expo install @react-native-community/datetimepicker
npx expo install expo-image-picker
npx expo install expo-auth-session expo-crypto

# Icons
npm install @expo/vector-icons
```

### 1.3 Configure app.json
```json
{
  "expo": {
    "name": "Employee Management App",
    "slug": "employee-management",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "light",
    "newArchEnabled": false,
    "splash": {
      "image": "./assets/splash-icon.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    },
    "ios": {
      "supportsTablet": true
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#ffffff"
      },
      "edgeToEdgeEnabled": true,
      "package": "com.yourcompany.employeeapp"
    },
    "web": {
      "favicon": "./assets/favicon.png"
    }
  }
}
```

## Step 2: Firebase Configuration

### 2.1 Create Firebase Project
1. Go to Firebase Console (https://console.firebase.google.com)
2. Create new project
3. Enable Authentication (Email/Password)
4. Create Firestore Database
5. Get configuration keys

### 2.2 Create Firebase Config File
Create `src/services/firebase.ts`:
```typescript
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "*********",
  appId: "your-app-id"
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
```

## Step 3: Project Structure

### 3.1 Create Directory Structure
```
src/
├── components/
│   └── CustomAppbar.tsx
├── navigation/
│   ├── AppNavigator.tsx
│   └── AuthStack.tsx
├── screens/
│   ├── auth/
│   │   ├── LoginScreen.tsx
│   │   └── SignupScreen.tsx
│   ├── dashboard/
│   │   ├── EmployeeDashboard.tsx
│   │   ├── ManagerDashboard.tsx
│   │   ├── SuperadminDashboard.tsx
│   │   └── EditProfileScreen.tsx
│   ├── directory/
│   │   └── DirectoryScreen.tsx
│   ├── leave/
│   │   ├── LeaveListScreen.tsx
│   │   └── LeaveApplyScreen.tsx
│   ├── announcements/
│   │   ├── AnnouncementListScreen.tsx
│   │   └── AnnouncementAddScreen.tsx
│   └── admin/
│       └── AdminPanelScreen.tsx
├── services/
│   ├── auth.ts
│   └── firebase.ts
├── utils/
│   └── departments.ts
└── theme.ts
```

## Step 4: Core Services Implementation

### 4.1 Authentication Service
Create `src/services/auth.ts`:
```typescript
import { auth } from './firebase';
import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as FirebaseUser 
} from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface User {
  id: string;
  email: string;
  name: string;
}

class AuthService {
  private currentUser: User | null = null;

  async signInWithEmailAndPassword(email: string, password: string): Promise<User> {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user: User = {
      id: userCredential.user.uid,
      email: userCredential.user.email!,
      name: email.split('@')[0]
    };
    
    this.currentUser = user;
    await AsyncStorage.setItem('user', JSON.stringify(user));
    return user;
  }

  async createUserWithEmailAndPassword(email: string, password: string): Promise<User> {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user: User = {
      id: userCredential.user.uid,
      email: userCredential.user.email!,
      name: email.split('@')[0]
    };
    
    this.currentUser = user;
    await AsyncStorage.setItem('user', JSON.stringify(user));
    return user;
  }

  async signOut(): Promise<void> {
    await firebaseSignOut(auth);
    this.currentUser = null;
    await AsyncStorage.removeItem('user');
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  onAuthStateChanged(callback: (user: User | null) => void) {
    return onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        const storedUser = await AsyncStorage.getItem('user');
        if (storedUser) {
          this.currentUser = JSON.parse(storedUser);
          callback(this.currentUser);
        }
      } else {
        this.currentUser = null;
        callback(null);
      }
    });
  }
}

export const authService = new AuthService();
```

### 4.2 Theme Configuration
Create `src/theme.ts`:
```typescript
import { MD3LightTheme as DefaultTheme } from 'react-native-paper';

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#1e3a8a',
    accent: '#22b0e6',
    secondary: '#f59e0b',
    background: '#fff',
    surface: '#fff',
    text: '#222',
    onSurface: '#333',
    placeholder: '#888',
    error: '#e53935',
  },
};

export default theme;
```

## Step 5: Main App Component

### 5.1 Update App.tsx
```typescript
import React from 'react';
import { PaperProvider } from 'react-native-paper';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import AppNavigator from './src/navigation/AppNavigator';
import theme from './src/theme';

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PaperProvider theme={theme}>
        <AppNavigator />
      </PaperProvider>
    </GestureHandlerRootView>
  );
}
```

## Step 6: Key Implementation Notes

### 6.1 Firestore Data Structure
```
users/ (collection)
├── {userId}/ (document)
│   ├── name: string
│   ├── email: string
│   ├── role: 'employee' | 'manager' | 'superadmin'
│   ├── department: string
│   ├── phone: string
│   └── photoURL: string

leaves/ (collection)
├── {leaveId}/ (document)
│   ├── userId: string
│   ├── fromDate: timestamp
│   ├── toDate: timestamp
│   ├── leaveType: string
│   ├── reason: string
│   ├── status: 'pending' | 'approved' | 'rejected'
│   └── createdAt: timestamp

announcements/ (collection)
├── {announcementId}/ (document)
│   ├── title: string
│   ├── message: string
│   ├── departments: string[]
│   ├── createdBy: string
│   └── createdAt: timestamp
```

### 6.2 Role-Based Access Control
- **Employee**: View own profile, apply for leave, view announcements
- **Manager**: Employee permissions + approve team leave requests, create announcements
- **Superadmin**: All permissions + user management, role changes, system administration

### 6.3 Navigation Structure
- Conditional navigation based on user roles
- Admin Panel only visible to superadmins
- Role-specific dashboards and permissions

## Step 7: Testing and Deployment

### 7.1 Testing Commands
```bash
# Start development server
npx expo start

# Test on device
npx expo start --go

# Clear cache if needed
npx expo start --clear
```

### 7.2 Common Issues and Solutions

#### Issue: Metro bundler errors
**Solution**: Clear cache and restart
```bash
npx expo start --clear
```

#### Issue: Firebase authentication not working
**Solution**: Check Firebase configuration and enable Authentication in Firebase Console

#### Issue: Navigation not working
**Solution**: Ensure all navigation dependencies are installed with `npx expo install`

#### Issue: Date picker not working
**Solution**: Install date picker with `npx expo install @react-native-community/datetimepicker`

## Step 8: Feature Implementation Priority

### Phase 1: Core Setup
1. Project initialization
2. Firebase configuration
3. Authentication system
4. Basic navigation

### Phase 2: User Management
1. Role-based dashboards
2. Profile management
3. User directory

### Phase 3: Leave Management
1. Leave application system
2. Approval workflow
3. Status tracking

### Phase 4: Additional Features
1. Announcements system
2. Admin panel
3. Role management

### Phase 5: Polish and Testing
1. UI/UX improvements
2. Error handling
3. Performance optimization
4. Testing across devices

## Step 9: Key Screen Implementation Examples

### 9.1 Login Screen Template
```typescript
import React, { useState } from 'react';
import { View, ScrollView } from 'react-native';
import { Text, TextInput, Button, Card } from 'react-native-paper';
import { authService } from '../../services/auth';

const LoginScreen = ({ navigation }: any) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    setLoading(true);
    try {
      await authService.signInWithEmailAndPassword(email, password);
    } catch (error: any) {
      alert(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={{ flex: 1, padding: 16 }}>
      <Card style={{ padding: 16 }}>
        <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 16 }}>
          Login
        </Text>
        <TextInput
          label="Email"
          value={email}
          onChangeText={setEmail}
          style={{ marginBottom: 16 }}
        />
        <TextInput
          label="Password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          style={{ marginBottom: 16 }}
        />
        <Button mode="contained" onPress={handleLogin} loading={loading}>
          Login
        </Button>
      </Card>
    </ScrollView>
  );
};
```

### 9.2 Navigation Setup Template
```typescript
import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { authService, User } from '../services/auth';
import AuthStack from './AuthStack';
import { ActivityIndicator } from 'react-native-paper';

const Drawer = createDrawerNavigator();

const AppNavigator = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = authService.onAuthStateChanged(async (user) => {
      setUser(user);
      if (user) {
        // Fetch user role from Firestore
        const userDoc = await getDoc(doc(db, 'users', user.id));
        const userRole = userDoc.exists() ? userDoc.data().role : 'employee';
        setRole(userRole);
      } else {
        setRole(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  if (loading) {
    return <ActivityIndicator style={{ flex: 1 }} />;
  }

  if (!user) {
    return <AuthStack />;
  }

  return (
    <NavigationContainer>
      <Drawer.Navigator>
        <Drawer.Screen name="Dashboard" component={DashboardScreen} />
        <Drawer.Screen name="Directory" component={DirectoryScreen} />
        <Drawer.Screen name="Leave Management" component={LeaveScreen} />
        {role === 'superadmin' && (
          <Drawer.Screen name="Admin Panel" component={AdminScreen} />
        )}
      </Drawer.Navigator>
    </NavigationContainer>
  );
};
```

## Step 10: Deployment Checklist

### 10.1 Pre-Deployment
- [ ] Test on multiple devices
- [ ] Verify Firebase security rules
- [ ] Test all user roles and permissions
- [ ] Validate form inputs and error handling
- [ ] Test offline scenarios
- [ ] Optimize images and assets

### 10.2 Production Build
```bash
# Build for production
eas build --platform all

# Submit to app stores
eas submit --platform all
```

## Conclusion

This comprehensive guide provides everything needed to implement a professional Employee Management App with Expo React Native. The guide includes:

✅ **Complete setup instructions**
✅ **All required dependencies**
✅ **Firebase configuration**
✅ **Authentication system**
✅ **Role-based access control**
✅ **Leave management system**
✅ **User directory with profiles**
✅ **Admin panel for user management**
✅ **Announcements system**
✅ **Professional UI/UX**

**Key Success Factors:**
1. Follow steps sequentially
2. Test each feature before moving to next
3. Use proper error handling
4. Implement role-based security
5. Test on real devices with Expo Go

For any issues, refer to:
- Expo Documentation: https://docs.expo.dev/
- React Navigation: https://reactnavigation.org/
- React Native Paper: https://reactnativepaper.com/
- Firebase Documentation: https://firebase.google.com/docs
