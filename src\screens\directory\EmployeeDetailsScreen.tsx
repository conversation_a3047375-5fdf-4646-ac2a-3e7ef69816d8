import React from 'react';
import { View, Text, Image, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  department?: string;
  role: string;
  photoURL?: string;
}

interface RouteParams {
  user: User;
}

const getRoleBadgeColor = (role: string) => {
  switch (role?.toLowerCase()) {
    case 'superadmin':
    case 'super admin':
      return '#dc2626'; // Red
    case 'manager':
      return '#f59e0b'; // Orange
    case 'employee':
    default:
      return '#10b981'; // Green
  }
};

export default function EmployeeDetailsScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = route.params as RouteParams;

  return (
    <View style={{ flex: 1, backgroundColor: '#f8f9fa' }}>
      {/* Header */}
      <View style={{ 
        backgroundColor: '#fff', 
        paddingHorizontal: 20, 
        paddingVertical: 16, 
        borderBottomWidth: 1, 
        borderBottomColor: '#e5e7eb',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Text style={{ fontSize: 18, fontWeight: '600', color: '#1f2937' }}>
          Employee Details
        </Text>
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          style={{
            padding: 8,
            borderRadius: 6,
            backgroundColor: '#f3f4f6'
          }}
        >
          <MaterialCommunityIcons name="arrow-left" size={20} color="#374151" />
        </TouchableOpacity>
      </View>

      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 20 }}>
        {/* Profile Section */}
        <View style={{
          backgroundColor: '#fff',
          borderRadius: 12,
          padding: 24,
          alignItems: 'center',
          marginBottom: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }}>
          {/* Profile Image */}
          {user.photoURL ? (
            <Image
              source={{ uri: user.photoURL }}
              style={{ 
                width: 100, 
                height: 100, 
                borderRadius: 50, 
                marginBottom: 16 
              }}
            />
          ) : (
            <View style={{
              width: 100,
              height: 100,
              borderRadius: 50,
              backgroundColor: '#e5e7eb',
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 16
            }}>
              <MaterialCommunityIcons
                name="account"
                size={50}
                color="#9ca3af"
              />
            </View>
          )}

          {/* Name */}
          <Text style={{ 
            fontSize: 22, 
            fontWeight: '700', 
            color: '#1f2937', 
            marginBottom: 8,
            textAlign: 'center'
          }}>
            {user.name || user.email}
          </Text>

          {/* Role Badge */}
          <View style={{
            backgroundColor: getRoleBadgeColor(user.role),
            paddingHorizontal: 16,
            paddingVertical: 6,
            borderRadius: 20,
          }}>
            <Text style={{
              color: '#fff',
              fontSize: 14,
              fontWeight: '600',
              textTransform: 'capitalize'
            }}>
              {user.role === 'superadmin' ? 'Admin' : user.role}
            </Text>
          </View>
        </View>

        {/* Contact Information */}
        <View style={{
          backgroundColor: '#fff',
          borderRadius: 12,
          padding: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
        }}>
          <Text style={{ 
            fontSize: 18, 
            fontWeight: '600', 
            color: '#1f2937', 
            marginBottom: 16 
          }}>
            Contact Information
          </Text>

          {/* Email */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 12,
            borderBottomWidth: 1,
            borderBottomColor: '#f3f4f6'
          }}>
            <MaterialCommunityIcons name="email" size={20} color="#6b7280" />
            <View style={{ marginLeft: 12, flex: 1 }}>
              <Text style={{ fontSize: 12, color: '#9ca3af', marginBottom: 2 }}>
                Email
              </Text>
              <Text style={{ fontSize: 14, color: '#1f2937', fontWeight: '500' }}>
                {user.email}
              </Text>
            </View>
          </View>

          {/* Mobile */}
          {user.phone && (
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 12,
              borderBottomWidth: 1,
              borderBottomColor: '#f3f4f6'
            }}>
              <MaterialCommunityIcons name="phone" size={20} color="#6b7280" />
              <View style={{ marginLeft: 12, flex: 1 }}>
                <Text style={{ fontSize: 12, color: '#9ca3af', marginBottom: 2 }}>
                  Mobile
                </Text>
                <Text style={{ fontSize: 14, color: '#1f2937', fontWeight: '500' }}>
                  {user.phone}
                </Text>
              </View>
            </View>
          )}

          {/* Department */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 12,
          }}>
            <MaterialCommunityIcons name="office-building" size={20} color="#6b7280" />
            <View style={{ marginLeft: 12, flex: 1 }}>
              <Text style={{ fontSize: 12, color: '#9ca3af', marginBottom: 2 }}>
                Department
              </Text>
              <Text style={{ fontSize: 14, color: '#1f2937', fontWeight: '500' }}>
                {user.department || 'Not specified'}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
