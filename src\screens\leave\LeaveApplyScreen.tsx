import React, { useState, useEffect } from 'react';
import { View, ScrollView, Platform, Alert } from 'react-native';
import { Text, Button, TextInput, HelperText, ActivityIndicator, Card, Menu, Divider } from 'react-native-paper';
import { createLeaveRequest } from '../../services/firebase';
import { authService } from '../../services/auth';
import { db } from '../../services/firebase';
import { doc, getDoc } from 'firebase/firestore';
import DateTimePicker from '@react-native-community/datetimepicker';

const LEAVE_TYPES = [
  { label: 'Casual Leave', value: 'casual' },
  { label: 'Sick Leave', value: 'sick' },
  { label: 'Annual Leave', value: 'annual' },
  { label: 'Emergency Leave', value: 'emergency' },
  { label: 'Maternity/Paternity Leave', value: 'maternity' },
  { label: 'Other', value: 'other' },
];

const LeaveApplyScreen = ({ navigation }: any) => {
  const [fromDate, setFromDate] = useState(new Date());
  const [toDate, setToDate] = useState(new Date());
  const [showFromDatePicker, setShowFromDatePicker] = useState(false);
  const [showToDatePicker, setShowToDatePicker] = useState(false);
  const [leaveType, setLeaveType] = useState('casual');
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [userProfile, setUserProfile] = useState<any>(null);
  const [typeMenuVisible, setTypeMenuVisible] = useState(false);

  useEffect(() => {
    const fetchUserProfile = async () => {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) return;

      try {
        const userDoc = await getDoc(doc(db, 'users', currentUser.id));
        if (userDoc.exists()) {
          setUserProfile(userDoc.data());
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, []);

  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };

  const calculateDays = () => {
    const diffTime = Math.abs(toDate.getTime() - fromDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates
    return diffDays;
  };

  const validateForm = () => {
    if (!reason.trim()) {
      setError('Please provide a reason for leave');
      return false;
    }
    if (fromDate > toDate) {
      setError('From date cannot be after To date');
      return false;
    }
    if (fromDate < new Date(new Date().setHours(0, 0, 0, 0))) {
      setError('Cannot apply for leave in the past');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError('');
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) throw new Error('Not authenticated');

      const leaveData = {
        userId: currentUser.id,
        userEmail: currentUser.email,
        userName: userProfile?.name || currentUser.name || '',
        userDepartment: userProfile?.department || '',
        fromDate: formatDate(fromDate),
        toDate: formatDate(toDate),
        leaveType,
        reason: reason.trim(),
        status: 'pending',
        totalDays: calculateDays(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      await createLeaveRequest(leaveData);
      navigation.goBack();
    } catch (e: any) {
      setError(e.message);
    } finally {
      setLoading(false);
    }
  };

  const onFromDateChange = (event: any, selectedDate?: Date) => {
    setShowFromDatePicker(false);
    if (selectedDate) {
      setFromDate(selectedDate);
      // Auto-adjust toDate if it's before fromDate
      if (selectedDate > toDate) {
        setToDate(selectedDate);
      }
    }
  };

  const onToDateChange = (event: any, selectedDate?: Date) => {
    setShowToDatePicker(false);
    if (selectedDate) {
      setToDate(selectedDate);
    }
  };

  const selectedLeaveType = LEAVE_TYPES.find(type => type.value === leaveType);

  return (
    <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 16 }}>
      <Card style={{ marginBottom: 16, elevation: 2 }}>
        <Card.Content>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#1e3a8a', marginBottom: 16 }}>
            Apply for Leave
          </Text>

          {/* User Info */}
          <View style={{ backgroundColor: '#f8fafc', padding: 12, borderRadius: 8, marginBottom: 16 }}>
            <Text style={{ fontWeight: '500', color: '#374151' }}>
              {userProfile?.name || 'Loading...'}
            </Text>
            <Text style={{ color: '#6b7280', fontSize: 13 }}>
              {userProfile?.department || 'No department'} • {userProfile?.email || authService.getCurrentUser()?.email}
            </Text>
          </View>

          {/* Date Selection */}
          <View style={{ marginBottom: 16 }}>
            <Text style={{ fontSize: 16, fontWeight: '500', marginBottom: 8, color: '#374151' }}>
              Leave Duration
            </Text>

            <View style={{ flexDirection: 'row', gap: 12, marginBottom: 8 }}>
              <View style={{ flex: 1 }}>
                <Button
                  mode="outlined"
                  onPress={() => setShowFromDatePicker(true)}
                  style={{ marginBottom: 4 }}
                >
                  From: {formatDate(fromDate)}
                </Button>
              </View>
              <View style={{ flex: 1 }}>
                <Button
                  mode="outlined"
                  onPress={() => setShowToDatePicker(true)}
                  style={{ marginBottom: 4 }}
                >
                  To: {formatDate(toDate)}
                </Button>
              </View>
            </View>

            <Text style={{ color: '#6b7280', fontSize: 13, textAlign: 'center' }}>
              Total Days: {calculateDays()}
            </Text>
          </View>

          {/* Leave Type Selection */}
          <View style={{ marginBottom: 16 }}>
            <Text style={{ fontSize: 16, fontWeight: '500', marginBottom: 8, color: '#374151' }}>
              Leave Type
            </Text>
            <Menu
              visible={typeMenuVisible}
              onDismiss={() => setTypeMenuVisible(false)}
              anchor={
                <Button
                  mode="outlined"
                  onPress={() => setTypeMenuVisible(true)}
                  style={{ justifyContent: 'flex-start' }}
                >
                  {selectedLeaveType?.label || 'Select Leave Type'}
                </Button>
              }
            >
              {LEAVE_TYPES.map((type) => (
                <Menu.Item
                  key={type.value}
                  onPress={() => {
                    setLeaveType(type.value);
                    setTypeMenuVisible(false);
                  }}
                  title={type.label}
                />
              ))}
            </Menu>
          </View>

          {/* Reason */}
          <TextInput
            label="Reason for Leave"
            value={reason}
            onChangeText={setReason}
            multiline
            numberOfLines={4}
            style={{ marginBottom: 16 }}
            placeholder="Please provide a detailed reason for your leave request..."
          />

          {error ? (
            <HelperText type="error" style={{ marginBottom: 16 }}>
              {error}
            </HelperText>
          ) : null}

          {/* Action Buttons */}
          <View style={{ flexDirection: 'row', gap: 12 }}>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              style={{ flex: 1 }}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSubmit}
              loading={loading}
              disabled={loading || !reason.trim()}
              style={{ flex: 1 }}
            >
              Submit Application
            </Button>
          </View>
        </Card.Content>
      </Card>

      {/* Date Pickers */}
      {showFromDatePicker && (
        <DateTimePicker
          value={fromDate}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={onFromDateChange}
          minimumDate={new Date()}
        />
      )}

      {showToDatePicker && (
        <DateTimePicker
          value={toDate}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={onToDateChange}
          minimumDate={fromDate}
        />
      )}
    </ScrollView>
  );
};

export default LeaveApplyScreen; 