import React, { useEffect, useState } from 'react';
import { <PERSON>, FlatList, <PERSON><PERSON>, ScrollView } from 'react-native';
import { Text, Button, Card, ActivityIndicator, IconButton, List, Portal, Dialog, Divider } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db, supabase } from '../../services/supabase';

interface Announcement {
  id: string;
  title: string;
  message: string;
  createdAt: string;
  createdBy: string;
  createdByName: string;
  departments: string[];
}

const AnnouncementListScreen = ({ navigation }: any) => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [role, setRole] = useState('employee');
  const [userId, setUserId] = useState('');
  const [department, setDepartment] = useState('');
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchUserAndAnnouncements();
    });
    return unsubscribe;
  }, [navigation]);

  const fetchUserAndAnnouncements = async () => {
    try {
      setLoading(true);
      const currentUser = supabaseAuthService.getCurrentUser();
      if (currentUser) {
        setUserId(currentUser.id);
        
        // Get user profile from Supabase
        const userProfile = await db.users.getById(currentUser.id);
        if (userProfile) {
          setRole(userProfile.role || 'employee');
          setDepartment(userProfile.department || '');
          await fetchAnnouncements(userProfile.role || 'employee', userProfile.department || '');
        }
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      setLoading(false);
    }
  };

  const fetchAnnouncements = async (userRole: string, userDept: string) => {
    try {
      const { data, error } = await supabase
        .from('announcements')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching announcements:', error);
        return;
      }

      const announcements: Announcement[] = data.map((a: any) => ({
        id: a.id,
        title: a.title || '',
        message: a.content || '', // Using 'content' column from database
        createdAt: a.created_at || '',
        createdBy: a.created_by || '',
        createdByName: a.created_by_name || 'Unknown User',
        departments: Array.isArray(a.departments) ? a.departments : [a.department || 'All Departments'],
      }));

      // Filter announcements based on user role and department
      let filtered = announcements;
      if (userRole === 'employee' || userRole === 'manager') {
        filtered = announcements.filter((a: Announcement) =>
          a.departments.includes('All Departments') || a.departments.includes(userDept)
        );
      }

      setAnnouncements(filtered);
    } catch (error) {
      console.error('Error fetching announcements:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    Alert.alert(
      'Delete Announcement',
      'Are you sure you want to delete this announcement?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('announcements')
                .delete()
                .eq('id', id);

              if (error) {
                console.error('Error deleting announcement:', error);
                Alert.alert('Error', 'Failed to delete announcement');
                return;
              }

              // Refresh announcements
              fetchUserAndAnnouncements();
              Alert.alert('Success', 'Announcement deleted successfully');
            } catch (error) {
              console.error('Error deleting announcement:', error);
              Alert.alert('Error', 'Failed to delete announcement');
            }
          },
        },
      ]
    );
  };

  const openAnnouncementModal = (announcement: Announcement) => {
    setSelectedAnnouncement(announcement);
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSelectedAnnouncement(null);
  };

  if (loading) return <ActivityIndicator style={{ marginTop: 32 }} />;

  return (
    <View style={{ flex: 1, padding: 16 }}>
      {/* Add Announcement Button for Managers and Super Admins */}
      {(role === 'superadmin' || role === 'manager') && (
        <Button 
          mode="contained" 
          onPress={() => navigation.navigate('AnnouncementAdd')} 
          style={{ marginBottom: 16 }}
          icon="plus"
        >
          Add Announcement
        </Button>
      )}
      
      <FlatList
        data={announcements}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <List.Item
            title={item.title}
            description={`By ${item.createdByName} • ${new Date(item.createdAt).toLocaleDateString()}`}
            left={props => <List.Icon {...props} icon="bullhorn" />}
            right={props => (
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {(role === 'superadmin' || role === 'manager') && item.createdBy === userId && (
                  <IconButton
                    icon="delete"
                    onPress={() => handleDelete(item.id)}
                    iconColor="#dc2626"
                    size={20}
                  />
                )}
                <List.Icon {...props} icon="chevron-right" />
              </View>
            )}
            onPress={() => openAnnouncementModal(item)}
            style={{
              backgroundColor: '#fff',
              marginBottom: 1,
              paddingVertical: 8
            }}
          />
        )}
        ListEmptyComponent={
          <Text style={{ marginTop: 32, textAlign: 'center', color: '#666' }}>
            No announcements found.
          </Text>
        }
      />

      {/* Announcement Details Modal */}
      <Portal>
        <Dialog
          visible={modalVisible}
          onDismiss={closeModal}
          style={{ maxHeight: '80%' }}
        >
          <Dialog.Title>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text style={{ fontSize: 18, fontWeight: 'bold', flex: 1 }}>
                {selectedAnnouncement?.title}
              </Text>
              {(role === 'superadmin' || role === 'manager') &&
               selectedAnnouncement &&
               selectedAnnouncement.createdBy === userId && (
                <IconButton
                  icon="delete"
                  onPress={() => {
                    closeModal();
                    handleDelete(selectedAnnouncement.id);
                  }}
                  iconColor="#dc2626"
                  size={20}
                />
              )}
            </View>
          </Dialog.Title>

          <Dialog.Content>
            {selectedAnnouncement && (
              <ScrollView style={{ maxHeight: 400 }}>
                <View style={{ marginBottom: 16 }}>
                  <Text style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
                    By {selectedAnnouncement.createdByName}
                  </Text>
                  <Text style={{ fontSize: 12, color: '#666', marginBottom: 12 }}>
                    {new Date(selectedAnnouncement.createdAt).toLocaleDateString()} at{' '}
                    {new Date(selectedAnnouncement.createdAt).toLocaleTimeString()}
                  </Text>
                </View>

                <Divider style={{ marginBottom: 16 }} />

                <Text style={{ fontSize: 16, lineHeight: 24, marginBottom: 16 }}>
                  {selectedAnnouncement.message}
                </Text>

                {selectedAnnouncement.departments.length > 0 && (
                  <>
                    <Divider style={{ marginBottom: 12 }} />
                    <View>
                      <Text style={{ fontSize: 12, fontWeight: 'bold', color: '#666', marginBottom: 4 }}>
                        Target Departments:
                      </Text>
                      <Text style={{ fontSize: 14, color: '#333' }}>
                        {selectedAnnouncement.departments.join(', ')}
                      </Text>
                    </View>
                  </>
                )}
              </ScrollView>
            )}
          </Dialog.Content>

          <Dialog.Actions>
            <Button onPress={closeModal}>Close</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

export default AnnouncementListScreen;
