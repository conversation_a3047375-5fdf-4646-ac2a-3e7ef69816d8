import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, RefreshControl, Alert, Image } from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  ActivityIndicator,
  Chip,
  Menu,
  IconButton,
  Divider,
  TouchableRipple
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { projectUpdatesService } from '../../services/projectUpdatesService';
import { ProjectUpdate, getUpdateVisibility } from '../../types/projectUpdates';

interface ProjectUpdatesListScreenProps {
  navigation: any;
}

const ProjectUpdatesListScreen: React.FC<ProjectUpdatesListScreenProps> = ({ navigation }) => {
  const [updates, setUpdates] = useState<ProjectUpdate[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [menuVisible, setMenuVisible] = useState<{ [key: string]: boolean }>({});
  
  const currentUser = supabaseAuthService.getCurrentUser();

  useEffect(() => {
    loadUpdates();
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadUpdates();
    }, [])
  );

  const loadUpdates = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      if (!currentUser) {
        throw new Error('No current user found');
      }

      const updatesData = await projectUpdatesService.getUpdatesForUser(
        currentUser.id,
        currentUser.role as 'employee' | 'manager' | 'superadmin',
        currentUser.managerId
      );

      setUpdates(updatesData);
    } catch (error) {
      console.error('Error loading project updates:', error);
      Alert.alert('Error', 'Failed to load project updates. Please try again.');
    } finally {
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  };

  const onRefresh = useCallback(() => {
    loadUpdates(true);
  }, []);

  const handleDeleteUpdate = async (updateId: string) => {
    Alert.alert(
      'Delete Update',
      'Are you sure you want to delete this project update?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              if (!currentUser) return;
              
              await projectUpdatesService.deleteProjectUpdate(
                updateId, 
                currentUser.id, 
                currentUser.role ?? ''
              );
              
              await loadUpdates();
              Alert.alert('Success', 'Project update deleted successfully.');
            } catch (error) {
              console.error('Error deleting update:', error);
              Alert.alert('Error', 'Failed to delete project update. Please try again.');
            }
          }
        }
      ]
    );
  };

  const toggleMenu = (updateId: string) => {
    setMenuVisible(prev => ({
      ...prev,
      [updateId]: !prev[updateId]
    }));
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canCreateUpdates = currentUser?.role === 'manager' || currentUser?.role === 'superadmin';

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading project updates...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Compact Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Project Updates</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {updates.length === 0 ? (
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons name="clipboard-text-off" size={48} color="#9ca3af" />
            <Text style={styles.emptyTitle}>No Updates Yet</Text>
            <Text style={styles.emptySubtitle}>
              {canCreateUpdates
                ? 'Share your first project update'
                : 'No updates have been shared yet'
              }
            </Text>
            {canCreateUpdates && (
              <Button
                mode="contained"
                onPress={() => navigation.navigate('AddProjectUpdate')}
                style={styles.emptyButton}
                labelStyle={styles.emptyButtonText}
              >
                Create Update
              </Button>
            )}
          </View>
        ) : (
          <View style={styles.updatesContainer}>
            {updates.map((update) => {
              const visibility = getUpdateVisibility(update, {
                id: currentUser?.id || '',
                role: currentUser?.role as 'employee' | 'manager' | 'superadmin',
                managerId: currentUser?.managerId
              });

              return (
                <View key={update.id} style={styles.updateCard}>
                  {/* Header with project and actions */}
                  <View style={styles.updateHeader}>
                    <View style={styles.updateHeaderLeft}>
                      <View style={styles.projectBadge}>
                        <MaterialCommunityIcons name="folder" size={14} color="#1e40af" />
                        <Text style={styles.projectBadgeText}>{update.projectName}</Text>
                      </View>
                      <Text style={styles.updateDate}>{formatDate(update.createdAt)}</Text>
                    </View>

                    {(visibility.canEdit || visibility.canDelete) && (
                      <Menu
                        visible={menuVisible[update.id] || false}
                        onDismiss={() => toggleMenu(update.id)}
                        anchor={
                          <TouchableRipple
                            onPress={() => toggleMenu(update.id)}
                            style={styles.menuButton}
                          >
                            <MaterialCommunityIcons name="dots-vertical" size={20} color="#6b7280" />
                          </TouchableRipple>
                        }
                      >
                        {visibility.canEdit && (
                          <Menu.Item
                            onPress={() => {
                              toggleMenu(update.id);
                              navigation.navigate('EditProjectUpdate', { updateId: update.id });
                            }}
                            title="Edit"
                            leadingIcon="pencil"
                          />
                        )}
                        {visibility.canDelete && (
                          <Menu.Item
                            onPress={() => {
                              toggleMenu(update.id);
                              handleDeleteUpdate(update.id);
                            }}
                            title="Delete"
                            leadingIcon="delete"
                          />
                        )}
                      </Menu>
                    )}
                  </View>

                  {/* Update content */}
                  <Text style={styles.updateTitle}>{update.heading}</Text>
                  <Text style={styles.updateDescription}>{update.description}</Text>

                  {/* Image if present */}
                  {update.imageUrl && (
                    <Image source={{ uri: update.imageUrl }} style={styles.updateImage} />
                  )}

                  {/* Footer with author */}
                  <View style={styles.updateFooter}>
                    <Text style={styles.authorText}>By {update.createdByName}</Text>
                  </View>
                </View>
              );
            })}
          </View>
        )}
      </ScrollView>

      {/* Floating Action Button for creating updates */}
      {canCreateUpdates && (
        <FAB
          style={styles.fab}
          icon="plus"
          onPress={() => navigation.navigate('AddProjectUpdate')}
          label="Add Update"
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  scrollView: {
    flex: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 8,
    textAlign: 'center',
  },
  emptyButton: {
    backgroundColor: '#1e40af',
    borderRadius: 8,
    marginTop: 20,
  },
  emptyButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  updatesContainer: {
    padding: 16,
  },
  updateCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  updateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  updateHeaderLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  projectBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#eff6ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  projectBadgeText: {
    fontSize: 12,
    color: '#1e40af',
    fontWeight: '500',
    marginLeft: 4,
  },
  updateDate: {
    fontSize: 12,
    color: '#6b7280',
  },
  menuButton: {
    padding: 4,
    borderRadius: 4,
  },
  updateTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  updateDescription: {
    fontSize: 14,
    color: '#4b5563',
    lineHeight: 20,
    marginBottom: 12,
  },
  updateImage: {
    width: '100%',
    height: 180,
    borderRadius: 8,
    marginBottom: 12,
  },
  updateFooter: {
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    paddingTop: 12,
  },
  authorText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  fab: {
    margin: 16,
    right: 0,
    bottom: 24,
    backgroundColor: '#1e40af',
  },
});

export default ProjectUpdatesListScreen;
