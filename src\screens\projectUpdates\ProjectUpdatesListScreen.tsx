import React, { useState, useEffect, useCallback } from 'react';
import { View, ScrollView, StyleSheet, RefreshControl, Alert, Image } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  FAB,
  ActivityIndicator,
  Chip,
  Menu,
  IconButton,
  Divider
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { projectUpdatesService } from '../../services/projectUpdatesService';
import { ProjectUpdate, getUpdateVisibility } from '../../types/projectUpdates';

interface ProjectUpdatesListScreenProps {
  navigation: any;
}

const ProjectUpdatesListScreen: React.FC<ProjectUpdatesListScreenProps> = ({ navigation }) => {
  const [updates, setUpdates] = useState<ProjectUpdate[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [menuVisible, setMenuVisible] = useState<{ [key: string]: boolean }>({});
  
  const currentUser = supabaseAuthService.getCurrentUser();

  useEffect(() => {
    loadUpdates();
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadUpdates();
    }, [])
  );

  const loadUpdates = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      if (!currentUser) {
        throw new Error('No current user found');
      }

      const updatesData = await projectUpdatesService.getUpdatesForUser(
        currentUser.id,
        currentUser.role as 'employee' | 'manager' | 'superadmin',
        currentUser.managerId
      );

      setUpdates(updatesData);
    } catch (error) {
      console.error('Error loading project updates:', error);
      Alert.alert('Error', 'Failed to load project updates. Please try again.');
    } finally {
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  };

  const onRefresh = useCallback(() => {
    loadUpdates(true);
  }, []);

  const handleDeleteUpdate = async (updateId: string) => {
    Alert.alert(
      'Delete Update',
      'Are you sure you want to delete this project update?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              if (!currentUser) return;
              
              await projectUpdatesService.deleteProjectUpdate(
                updateId, 
                currentUser.id, 
                currentUser.role ?? ''
              );
              
              await loadUpdates();
              Alert.alert('Success', 'Project update deleted successfully.');
            } catch (error) {
              console.error('Error deleting update:', error);
              Alert.alert('Error', 'Failed to delete project update. Please try again.');
            }
          }
        }
      ]
    );
  };

  const toggleMenu = (updateId: string) => {
    setMenuVisible(prev => ({
      ...prev,
      [updateId]: !prev[updateId]
    }));
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canCreateUpdates = currentUser?.role === 'manager' || currentUser?.role === 'superadmin';

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading project updates...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <Card style={styles.headerCard}>
        <Card.Content>
          <View style={styles.headerContent}>
            <MaterialCommunityIcons name="clipboard-text" size={32} color="#1e3a8a" />
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>Project Updates</Text>
              <Text style={styles.headerSubtitle}>
                {currentUser?.role === 'employee' 
                  ? 'Updates from your team and management'
                  : currentUser?.role === 'manager'
                  ? 'Your team updates and company-wide updates'
                  : 'All project updates across the organization'
                }
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {updates.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <MaterialCommunityIcons name="clipboard-text-off" size={64} color="#ccc" />
              <Text style={styles.emptyTitle}>No Project Updates</Text>
              <Text style={styles.emptySubtitle}>
                {canCreateUpdates 
                  ? 'Be the first to share a project update!'
                  : 'No updates have been shared yet.'
                }
              </Text>
              {canCreateUpdates && (
                <Button
                  mode="contained"
                  onPress={() => navigation.navigate('AddProjectUpdate')}
                  style={styles.emptyButton}
                  icon="plus"
                >
                  Create Update
                </Button>
              )}
            </Card.Content>
          </Card>
        ) : (
          updates.map((update) => {
            const visibility = getUpdateVisibility(update, {
              id: currentUser?.id || '',
              role: currentUser?.role as 'employee' | 'manager' | 'superadmin',
              managerId: currentUser?.managerId
            });

            return (
              <Card key={update.id} style={styles.updateCard}>
                <Card.Content>
                  {/* Header with project and actions */}
                  <View style={styles.updateHeader}>
                    <View style={styles.updateHeaderLeft}>
                      <Chip 
                        icon="folder" 
                        style={styles.projectChip}
                        textStyle={styles.projectChipText}
                      >
                        {update.projectName}
                      </Chip>
                      
                    </View>
                    
                    {(visibility.canEdit || visibility.canDelete) && (
                      <Menu
                        visible={menuVisible[update.id] || false}
                        onDismiss={() => toggleMenu(update.id)}
                        anchor={
                          <IconButton
                            icon="dots-vertical"
                            onPress={() => toggleMenu(update.id)}
                          />
                        }
                      >
                        {visibility.canEdit && (
                          <Menu.Item
                            onPress={() => {
                              toggleMenu(update.id);
                              navigation.navigate('EditProjectUpdate', { updateId: update.id });
                            }}
                            title="Edit"
                            leadingIcon="pencil"
                          />
                        )}
                        {visibility.canDelete && (
                          <Menu.Item
                            onPress={() => {
                              toggleMenu(update.id);
                              handleDeleteUpdate(update.id);
                            }}
                            title="Delete"
                            leadingIcon="delete"
                          />
                        )}
                      </Menu>
                    )}
                  </View>

                  {/* Update content */}
                  <Text style={styles.updateTitle}>{update.heading}</Text>
                  <Text style={styles.updateDescription}>{update.description}</Text>

                  {/* Image if present */}
                  {update.imageUrl && (
                    <Image source={{ uri: update.imageUrl }} style={styles.updateImage} />
                  )}

                  <Divider style={styles.divider} />

                  {/* Footer with author and date */}
                  <View style={styles.updateFooter}>
                    <Text style={styles.authorText}>
                      By {update.createdByName}
                    </Text>
                    <Text style={styles.dateText}>
                      {formatDate(update.createdAt)}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            );
          })
        )}
      </ScrollView>

      {/* Floating Action Button for creating updates */}
      {canCreateUpdates && (
        <FAB
          style={styles.fab}
          icon="plus"
          onPress={() => navigation.navigate('AddProjectUpdate')}
          label="Add Update"
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    marginLeft: 16,
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e3a8a',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  emptyCard: {
    marginTop: 32,
    elevation: 1,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  emptyButton: {
    marginTop: 16,
  },
  updateCard: {
    marginBottom: 16,
    elevation: 1,
  },
  updateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  updateHeaderLeft: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    flex: 1,
    gap: 8,
  },
  projectChip: {
    backgroundColor: '#e3f2fd',
  },
  projectChipText: {
    color: '#1565c0',
    fontSize: 12,
  },
  roleChip: {
    marginLeft: 8,
  },
  superadminChip: {
    backgroundColor: '#fff3e0',
  },
  managerChip: {
    backgroundColor: '#f3e5f5',
  },
  roleChipText: {
    fontSize: 12,
  },
  updateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  updateDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  updateImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 12,
  },
  divider: {
    marginVertical: 12,
  },
  updateFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  authorText: {
    fontSize: 12,
    color: '#1e3a8a',
    fontWeight: '500',
  },
  dateText: {
    fontSize: 12,
    color: '#999',
  },
  fab: {
    margin: 16,
    right: 0,
    bottom: 24,
    backgroundColor: '#dbf5ff',
  },
});

export default ProjectUpdatesListScreen;
