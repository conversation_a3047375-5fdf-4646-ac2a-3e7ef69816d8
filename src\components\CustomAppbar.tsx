import React, { useState } from 'react';
import { View, Image } from 'react-native';
import { Appbar, Menu } from 'react-native-paper';
import { supabaseAuthService } from '../services/supabaseAuth';

const LOGO = require('../../assets/texam-logo.png');

const CustomAppbar = ({ navigation }: any) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  return (
    <Appbar.Header style={{ backgroundColor: '#fff', elevation: 2 }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
        <Appbar.Action icon="menu" color="#1e3a8a" onPress={navigation?.openDrawer ? navigation.openDrawer : undefined} />
        <Image source={LOGO} style={{ marginLeft: 8, marginRight: 8, resizeMode: 'contain' }} />
        <View style={{ flex: 1 }} />
        <Appbar.Action icon="bell-outline" color="#1e3a8a" />
        <Menu
          visible={menuVisible}
          onDismiss={closeMenu}
          anchor={<Appbar.Action icon="account-circle" color="#1e3a8a" onPress={openMenu} />}>
          <Menu.Item onPress={() => { closeMenu(); navigation.navigate('EditProfile'); }} leadingIcon="account" title="Profile" />
          <Menu.Item onPress={() => { closeMenu(); supabaseAuthService.signOut(); }} leadingIcon="logout" title="Logout" />
        </Menu>
      </View>
    </Appbar.Header>
  );
};

export default CustomAppbar; 