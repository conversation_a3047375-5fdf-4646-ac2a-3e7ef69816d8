import React, { useState } from 'react';
import { View, Image, Text } from 'react-native';
import { Appbar, Menu } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { supabaseAuthService } from '../services/supabaseAuth';

const LOGO = require('../../assets/texam-logo.png');

const CustomAppbar = ({ navigation }: any) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  return (
    <Appbar.Header style={{ backgroundColor: '#f8f9fa', elevation: 1, height: 64 }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1, paddingHorizontal: 8 }}>
        <Appbar.Action
          icon="menu"
          iconColor="#666"
          size={20}
          onPress={navigation?.openDrawer ? navigation.openDrawer : undefined}
        />
        <Image
          source={LOGO}
          style={{
            marginLeft: 4,
            marginRight: 8,
            resizeMode: 'contain',
            height: 28,
            width: 120
          }}
        />
        <View style={{ flex: 1 }} />

        {/* Notification with badge */}
        <View style={{ position: 'relative', marginRight: 8 }}>
          <MaterialCommunityIcons name="bell-outline" size={20} color="#666" />
          <View style={{
            position: 'absolute',
            top: -4,
            right: -4,
            backgroundColor: '#ef4444',
            borderRadius: 8,
            width: 16,
            height: 16,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
            <Text style={{ color: '#fff', fontSize: 10, fontWeight: 'bold' }}>2</Text>
          </View>
        </View>

        {/* Profile menu */}
        <Menu
          visible={menuVisible}
          onDismiss={closeMenu}
          anchor={
            <View style={{
              width: 32,
              height: 32,
              borderRadius: 16,
              backgroundColor: '#1e3a8a',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
              <MaterialCommunityIcons
                name="account"
                size={20}
                color="#fff"
                onPress={openMenu}
              />
            </View>
          }>
          <Menu.Item
            onPress={() => { closeMenu(); navigation.navigate('EditProfile'); }}
            leadingIcon="account"
            title="Profile"
            titleStyle={{ fontSize: 14 }}
          />
          <Menu.Item
            onPress={() => { closeMenu(); supabaseAuthService.signOut(); }}
            leadingIcon="logout"
            title="Logout"
            titleStyle={{ fontSize: 14 }}
          />
        </Menu>
      </View>
    </Appbar.Header>
  );
};

export default CustomAppbar; 