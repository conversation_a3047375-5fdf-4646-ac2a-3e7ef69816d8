import React, { useEffect, useState, useCallback } from 'react';
import { View, Image, ScrollView } from 'react-native';
import { Text, Button, ActivityIndicator, Card, Appbar, Menu } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

interface UserProfile {
  name: string;
  email: string;
  phone?: string;
  department?: string;
  photoURL?: string;
  status?: string;
  designation?: string;
  project?: string;
}

const LOGO = require('../../../assets/texam-logo.png');

const EmployeeDashboard = ({ navigation }: any) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);

  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const fetchProfile = useCallback(async () => {
    setLoading(true);
    const currentUser = supabaseAuthService.getCurrentUser();
    if (!currentUser) return;

    console.log('Fetching profile for user:', currentUser.id);

    try {
      const userData = await db.users.getById(currentUser.id);
      if (userData) {
        const profileData: UserProfile = {
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          department: userData.department,
          photoURL: userData.photo_url,
        };
        console.log('Profile found in Supabase:', profileData);
        setProfile(profileData);
      } else {
        console.log('No profile found in Supabase');
        // If no profile exists, create a basic profile from current user
        setProfile({
          name: currentUser.name || '',
          email: currentUser.email,
          phone: '',
          department: '',
          photoURL: '',
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      // Fallback to current user info
      const currentUser = supabaseAuthService.getCurrentUser();
      if (currentUser) {
        setProfile({
          name: currentUser.name || '',
          email: currentUser.email,
          phone: '',
          department: '',
          photoURL: '',
        });
      }
    }

    setLoading(false);
  }, []);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  // Refresh profile when screen comes into focus (e.g., after editing profile)
  useFocusEffect(
    useCallback(() => {
      fetchProfile();
    }, [fetchProfile])
  );

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator />
      </View>
    );
  }

  // Remove the "No profile found" check since we now always have a profile

  return (
    <View style={{ flex: 1, backgroundColor: '#f8f9fa' }}>
      <ScrollView style={{ width: '100%' }} contentContainerStyle={{ paddingBottom: 24, paddingTop: 16 }}>
        <View style={{ paddingHorizontal: 16 }}>

          {/* Welcome Header */}
          <View style={{ marginBottom: 20 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#1a1a1a', marginBottom: 4 }}>
              Good Morning, {profile?.name?.split(' ')[0] || 'User'}!
            </Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{ width: 8, height: 8, borderRadius: 4, backgroundColor: '#10b981', marginRight: 6 }} />
              <Text style={{ fontSize: 12, color: '#666', marginRight: 16 }}>System Online</Text>
              <MaterialCommunityIcons name="clock-outline" size={14} color="#666" style={{ marginRight: 4 }} />
              <Text style={{ fontSize: 12, color: '#666' }}>
                {new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
              </Text>
            </View>
          </View>

          {/* Dashboard List */}

          {/* Team Members */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#1e3a8a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="account-group" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>4</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Team Members</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '80%', backgroundColor: '#1e3a8a', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* My Tasks */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#6366f1',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="check-circle-outline" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>0</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>My Tasks</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '30%', backgroundColor: '#6366f1', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Pending Leaves */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#f59e0b',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="calendar-clock" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>1</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Pending Leaves</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Awaiting</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '20%', backgroundColor: '#f59e0b', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Active Tasks */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#10b981',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="checkbox-marked-circle-outline" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>1</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Active Tasks</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Progress</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '70%', backgroundColor: '#10b981', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Employee Directory */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#1e3a8a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="account-group" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>Employee Directory</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>Manage and Access Employee Information</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>

          {/* Project Updates */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#f59e0b',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="briefcase-outline" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>Project Updates</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>Stay Informed with Project uUpdates</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>

          {/* Leave Management */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#1e3a8a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="calendar-check" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>Leave Management</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>Leave Requests and Approvals</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>

          {/* Announcements */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#f59e0b',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="bullhorn-outline" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>Announcements</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>Prioritize Tasks Effectively</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
};

export default EmployeeDashboard; 