import React, { useEffect, useState, useCallback } from 'react';
import { View, Image, ScrollView } from 'react-native';
import { Text, Button, ActivityIndicator, Card, Appbar, ProgressBar, Menu } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

interface UserProfile {
  name: string;
  email: string;
  phone?: string;
  department?: string;
  photoURL?: string;
  status?: string;
  designation?: string;
  project?: string;
}

const LOGO = require('../../../assets/texam-logo.png');

const EmployeeDashboard = ({ navigation }: any) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);

  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const fetchProfile = useCallback(async () => {
    setLoading(true);
    const currentUser = supabaseAuthService.getCurrentUser();
    if (!currentUser) return;

    console.log('Fetching profile for user:', currentUser.id);

    try {
      const userData = await db.users.getById(currentUser.id);
      if (userData) {
        const profileData: UserProfile = {
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          department: userData.department,
          photoURL: userData.photo_url,
        };
        console.log('Profile found in Supabase:', profileData);
        setProfile(profileData);
      } else {
        console.log('No profile found in Supabase');
        // If no profile exists, create a basic profile from current user
        setProfile({
          name: currentUser.name || '',
          email: currentUser.email,
          phone: '',
          department: '',
          photoURL: '',
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      // Fallback to current user info
      const currentUser = supabaseAuthService.getCurrentUser();
      if (currentUser) {
        setProfile({
          name: currentUser.name || '',
          email: currentUser.email,
          phone: '',
          department: '',
          photoURL: '',
        });
      }
    }

    setLoading(false);
  }, []);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  // Refresh profile when screen comes into focus (e.g., after editing profile)
  useFocusEffect(
    useCallback(() => {
      fetchProfile();
    }, [fetchProfile])
  );

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator />
      </View>
    );
  }

  // Remove the "No profile found" check since we now always have a profile

  return (
    <View style={{ flex: 1, backgroundColor: '#f8f9fa' }}>
      <ScrollView style={{ width: '100%' }} contentContainerStyle={{ alignItems: 'center', paddingBottom: 24, paddingTop: 16 }}>
        <View style={{ width: '100%', maxWidth: 420, paddingHorizontal: 16 }}>
          {/* Profile Card */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                {profile?.photoURL ? (
                  <Image
                    source={{ uri: profile.photoURL }}
                    style={{ width: 48, height: 48, borderRadius: 24, marginRight: 12 }}
                  />
                ) : (
                  <View style={{
                    width: 48,
                    height: 48,
                    borderRadius: 24,
                    backgroundColor: '#1e3a8a',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: 12
                  }}>
                    <MaterialCommunityIcons name="account" size={24} color="#fff" />
                  </View>
                )}
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#1e3a8a', marginBottom: 2 }}>
                    {profile?.name || 'Welcome!'}
                  </Text>
                  <Text style={{ fontSize: 12, color: '#666', marginBottom: 1 }}>
                    {profile?.email}
                  </Text>
                  {profile?.department && (
                    <Text style={{ fontSize: 11, color: '#888' }}>
                      {profile.department}
                    </Text>
                  )}
                  {profile?.phone && (
                    <Text style={{ fontSize: 11, color: '#888' }}>
                      {profile.phone}
                    </Text>
                  )}
                </View>
              </View>
              {(!profile?.name || !profile?.phone || !profile?.department) && (
                <View style={{
                  backgroundColor: '#fef3c7',
                  padding: 8,
                  borderRadius: 4,
                  borderLeftWidth: 3,
                  borderLeftColor: '#f59e0b'
                }}>
                  <Text style={{ color: '#92400e', fontSize: 11, fontWeight: '500' }}>
                    📝 Complete your profile to get the most out of the app!
                  </Text>
                </View>
              )}
            </Card.Content>
          </Card>

          {/* Dashboard Grid */}
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
            {/* Team Members */}
            <Card style={{ width: '48%', marginBottom: 10, borderRadius: 6, elevation: 1, backgroundColor: '#ffffff' }}>
              <Card.Content style={{ padding: 12 }}>
                <View style={{ alignItems: 'center' }}>
                  <MaterialCommunityIcons name="account-group" size={24} color="#1e3a8a" style={{ marginBottom: 6 }} />
                  <Text style={{ fontWeight: '600', color: '#1e3a8a', fontSize: 13, textAlign: 'center', marginBottom: 2 }}>Team Members</Text>
                  <Text style={{ color: '#666', fontSize: 18, fontWeight: 'bold' }}>4</Text>
                </View>
              </Card.Content>
            </Card>

            {/* My Tasks */}
            <Card style={{ width: '48%', marginBottom: 10, borderRadius: 6, elevation: 1, backgroundColor: '#ffffff' }}>
              <Card.Content style={{ padding: 12 }}>
                <View style={{ alignItems: 'center' }}>
                  <MaterialCommunityIcons name="check-circle-outline" size={24} color="#6366f1" style={{ marginBottom: 6 }} />
                  <Text style={{ fontWeight: '600', color: '#6366f1', fontSize: 13, textAlign: 'center', marginBottom: 2 }}>My Tasks</Text>
                  <Text style={{ color: '#666', fontSize: 18, fontWeight: 'bold' }}>0</Text>
                </View>
              </Card.Content>
            </Card>

            {/* My Team */}
            <Card style={{ width: '48%', marginBottom: 10, borderRadius: 6, elevation: 1, backgroundColor: '#ffffff' }}>
              <Card.Content style={{ padding: 12 }}>
                <View style={{ alignItems: 'center' }}>
                  <MaterialCommunityIcons name="account-group-outline" size={24} color="#10b981" style={{ marginBottom: 6 }} />
                  <Text style={{ fontWeight: '600', color: '#10b981', fontSize: 13, textAlign: 'center', marginBottom: 2 }}>My Team</Text>
                  <Text style={{ color: '#666', fontSize: 18, fontWeight: 'bold' }}>1</Text>
                </View>
              </Card.Content>
            </Card>

            {/* Pending Leaves */}
            <Card style={{ width: '48%', marginBottom: 10, borderRadius: 6, elevation: 1, backgroundColor: '#ffffff' }}>
              <Card.Content style={{ padding: 12 }}>
                <View style={{ alignItems: 'center' }}>
                  <MaterialCommunityIcons name="calendar-clock" size={24} color="#f59e0b" style={{ marginBottom: 6 }} />
                  <Text style={{ fontWeight: '600', color: '#f59e0b', fontSize: 13, textAlign: 'center', marginBottom: 2 }}>Pending Leaves</Text>
                  <Text style={{ color: '#666', fontSize: 18, fontWeight: 'bold' }}>1</Text>
                </View>
              </Card.Content>
            </Card>

            {/* Active Tasks */}
            <Card style={{ width: '48%', marginBottom: 10, borderRadius: 6, elevation: 1, backgroundColor: '#ffffff' }}>
              <Card.Content style={{ padding: 12 }}>
                <View style={{ alignItems: 'center' }}>
                  <MaterialCommunityIcons name="checkbox-marked-circle-outline" size={24} color="#22b0e6" style={{ marginBottom: 6 }} />
                  <Text style={{ fontWeight: '600', color: '#22b0e6', fontSize: 13, textAlign: 'center', marginBottom: 2 }}>Active Tasks</Text>
                  <Text style={{ color: '#666', fontSize: 18, fontWeight: 'bold' }}>1</Text>
                </View>
              </Card.Content>
            </Card>

            {/* Announcements */}
            <Card style={{ width: '48%', marginBottom: 10, borderRadius: 6, elevation: 1, backgroundColor: '#ffffff' }}>
              <Card.Content style={{ padding: 12 }}>
                <View style={{ alignItems: 'center' }}>
                  <MaterialCommunityIcons name="bullhorn-outline" size={24} color="#a855f7" style={{ marginBottom: 6 }} />
                  <Text style={{ fontWeight: '600', color: '#a855f7', fontSize: 13, textAlign: 'center', marginBottom: 2 }}>Announcements</Text>
                  <Text style={{ color: '#666', fontSize: 18, fontWeight: 'bold' }}>1</Text>
                </View>
              </Card.Content>
            </Card>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default EmployeeDashboard; 