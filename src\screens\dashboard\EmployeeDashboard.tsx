import React, { useEffect, useState, useCallback } from 'react';
import { View, Image, ScrollView } from 'react-native';
import { Text, Button, ActivityIndicator, Card, Appbar, ProgressBar, Menu } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';

interface UserProfile {
  name: string;
  email: string;
  phone?: string;
  department?: string;
  photoURL?: string;
  status?: string;
  designation?: string;
  project?: string;
}

const LOGO = require('../../../assets/texam-logo.png');

const EmployeeDashboard = ({ navigation }: any) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [menuVisible, setMenuVisible] = useState(false);

  const openMenu = () => setMenuVisible(true);
  const closeMenu = () => setMenuVisible(false);

  const fetchProfile = useCallback(async () => {
    setLoading(true);
    const currentUser = supabaseAuthService.getCurrentUser();
    if (!currentUser) return;

    console.log('Fetching profile for user:', currentUser.id);

    try {
      const userData = await db.users.getById(currentUser.id);
      if (userData) {
        const profileData: UserProfile = {
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          department: userData.department,
          photoURL: userData.photo_url,
        };
        console.log('Profile found in Supabase:', profileData);
        setProfile(profileData);
      } else {
        console.log('No profile found in Supabase');
        // If no profile exists, create a basic profile from current user
        setProfile({
          name: currentUser.name || '',
          email: currentUser.email,
          phone: '',
          department: '',
          photoURL: '',
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      // Fallback to current user info
      const currentUser = supabaseAuthService.getCurrentUser();
      if (currentUser) {
        setProfile({
          name: currentUser.name || '',
          email: currentUser.email,
          phone: '',
          department: '',
          photoURL: '',
        });
      }
    }

    setLoading(false);
  }, []);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  // Refresh profile when screen comes into focus (e.g., after editing profile)
  useFocusEffect(
    useCallback(() => {
      fetchProfile();
    }, [fetchProfile])
  );

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator />
      </View>
    );
  }

  // Remove the "No profile found" check since we now always have a profile

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <ScrollView style={{ width: '100%' }} contentContainerStyle={{ alignItems: 'center', paddingBottom: 32 }}>
        <View style={{ width: '100%', maxWidth: 500 }}>
          {/* Profile Card */}
          <Card style={{ marginBottom: 16, borderRadius: 16, elevation: 2, backgroundColor: '#f8fafc' }}>
            <Card.Content style={{ padding: 20 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                {profile?.photoURL ? (
                  <Image
                    source={{ uri: profile.photoURL }}
                    style={{ width: 60, height: 60, borderRadius: 30, marginRight: 16 }}
                  />
                ) : (
                  <View style={{
                    width: 60,
                    height: 60,
                    borderRadius: 30,
                    backgroundColor: '#1e3a8a',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: 16
                  }}>
                    <MaterialCommunityIcons name="account" size={30} color="#fff" />
                  </View>
                )}
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#1e3a8a', marginBottom: 4 }}>
                    {profile?.name || 'Welcome!'}
                  </Text>
                  <Text style={{ fontSize: 14, color: '#666', marginBottom: 2 }}>
                    {profile?.email}
                  </Text>
                  {profile?.department && (
                    <Text style={{ fontSize: 13, color: '#888' }}>
                      {profile.department}
                    </Text>
                  )}
                  {profile?.phone && (
                    <Text style={{ fontSize: 13, color: '#888' }}>
                      {profile.phone}
                    </Text>
                  )}
                </View>
              </View>
              {(!profile?.name || !profile?.phone || !profile?.department) && (
                <View style={{
                  backgroundColor: '#fef3c7',
                  padding: 12,
                  borderRadius: 8,
                  borderLeftWidth: 4,
                  borderLeftColor: '#f59e0b'
                }}>
                  <Text style={{ color: '#92400e', fontSize: 13, fontWeight: '500' }}>
                    📝 Complete your profile to get the most out of the app!
                  </Text>
                </View>
              )}
            </Card.Content>
          </Card>

          {/* Team Members */}
          <Card style={{ marginBottom: 16, borderRadius: 16, elevation: 1 }}>
            <Card.Content style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MaterialCommunityIcons name="account-group" size={32} color="#1e3a8a" style={{ marginRight: 16 }} />
              <View style={{ flex: 1 }}>
                <Text style={{ fontWeight: 'bold', color: '#1e3a8a', fontSize: 16 }}>Team Members</Text>
                <Text style={{ color: '#666', fontSize: 13 }}>4</Text>
                <ProgressBar progress={0.8} color="#1e3a8a" style={{ height: 6, borderRadius: 3, marginTop: 8 }} />
              </View>
            </Card.Content>
          </Card>
          {/* My Tasks */}
          <Card style={{ marginBottom: 16, borderRadius: 16, elevation: 1 }}>
            <Card.Content style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MaterialCommunityIcons name="check-circle-outline" size={32} color="#6366f1" style={{ marginRight: 16 }} />
              <View style={{ flex: 1 }}>
                <Text style={{ fontWeight: 'bold', color: '#6366f1', fontSize: 16 }}>My Tasks</Text>
                <Text style={{ color: '#666', fontSize: 13 }}>0</Text>
                <ProgressBar progress={0.3} color="#6366f1" style={{ height: 6, borderRadius: 3, marginTop: 8 }} />
              </View>
            </Card.Content>
          </Card>
          {/* My Team */}
          <Card style={{ marginBottom: 16, borderRadius: 16, elevation: 1 }}>
            <Card.Content style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MaterialCommunityIcons name="account-group-outline" size={32} color="#10b981" style={{ marginRight: 16 }} />
              <View style={{ flex: 1 }}>
                <Text style={{ fontWeight: 'bold', color: '#10b981', fontSize: 16 }}>My Team</Text>
                <Text style={{ color: '#666', fontSize: 13 }}>1</Text>
                <ProgressBar progress={0.6} color="#10b981" style={{ height: 6, borderRadius: 3, marginTop: 8 }} />
              </View>
            </Card.Content>
          </Card>
          {/* Pending Leaves */}
          <Card style={{ marginBottom: 16, borderRadius: 16, elevation: 1 }}>
            <Card.Content style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MaterialCommunityIcons name="calendar-clock" size={32} color="#f59e0b" style={{ marginRight: 16 }} />
              <View style={{ flex: 1 }}>
                <Text style={{ fontWeight: 'bold', color: '#f59e0b', fontSize: 16 }}>Pending Leaves</Text>
                <Text style={{ color: '#666', fontSize: 13 }}>1</Text>
                <ProgressBar progress={0.2} color="#f59e0b" style={{ height: 6, borderRadius: 3, marginTop: 8 }} />
              </View>
            </Card.Content>
          </Card>
          {/* Active Tasks */}
          <Card style={{ marginBottom: 16, borderRadius: 16, elevation: 1 }}>
            <Card.Content style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MaterialCommunityIcons name="checkbox-marked-circle-outline" size={32} color="#22b0e6" style={{ marginRight: 16 }} />
              <View style={{ flex: 1 }}>
                <Text style={{ fontWeight: 'bold', color: '#22b0e6', fontSize: 16 }}>Active Tasks</Text>
                <Text style={{ color: '#666', fontSize: 13 }}>1</Text>
                <ProgressBar progress={0.7} color="#22b0e6" style={{ height: 6, borderRadius: 3, marginTop: 8 }} />
              </View>
            </Card.Content>
          </Card>
          {/* Announcements */}
          <Card style={{ marginBottom: 16, borderRadius: 16, elevation: 1 }}>
            <Card.Content style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MaterialCommunityIcons name="bullhorn-outline" size={32} color="#a855f7" style={{ marginRight: 16 }} />
              <View style={{ flex: 1 }}>
                <Text style={{ fontWeight: 'bold', color: '#a855f7', fontSize: 16 }}>Announcements</Text>
                <Text style={{ color: '#666', fontSize: 13 }}>1</Text>
                <ProgressBar progress={0.5} color="#a855f7" style={{ height: 6, borderRadius: 3, marginTop: 8 }} />
              </View>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
};

export default EmployeeDashboard; 