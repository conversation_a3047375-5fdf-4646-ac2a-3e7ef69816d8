import 'react-native-get-random-values';
import * as React from 'react';
import { Provider as PaperProvider } from 'react-native-paper';
import AppNavigator from './src/navigation/AppNavigator';
import theme from './src/theme';

// Suppress React Native Paper compact prop warnings
import { LogBox } from 'react-native';
LogBox.ignoreLogs([
  'Warning: Invalid prop `compact` supplied to `React.Fragment`',
]);

export default function App() {
  return (
    <PaperProvider theme={theme}>
      <AppNavigator />
    </PaperProvider>
  );
}