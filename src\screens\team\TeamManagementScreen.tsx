import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  Searchbar, 
  List, 
  Avatar, 
  IconButton, 
  Chip,
  ActivityIndicator,
  Divider
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { teamService, Employee } from '../../services/teamService';

const TeamManagementScreen: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<Employee[]>([]);
  const [availableEmployees, setAvailableEmployees] = useState<Employee[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [showAddMembers, setShowAddMembers] = useState(false);
  
  const currentUser = supabaseAuthService.getCurrentUser();

  useEffect(() => {
    loadTeamData();
  }, []);

  const loadTeamData = async () => {
    setLoading(true);
    try {
      if (!currentUser?.id) {
        throw new Error('No current user found');
      }

      // Load team members and available employees from Firebase
      const [teamMembersData, availableEmployeesData] = await Promise.all([
        teamService.getTeamMembers(currentUser.id),
        teamService.getAvailableEmployees()
      ]);

      setTeamMembers(teamMembersData);
      setAvailableEmployees(availableEmployeesData);
    } catch (error) {
      console.error('Error loading team data:', error);
      Alert.alert('Error', 'Failed to load team data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const addToTeam = async (employee: Employee) => {
    try {
      if (!currentUser?.id) {
        throw new Error('No current user found');
      }

      await teamService.assignEmployeeToManager(employee.id, currentUser.id);

      // Refresh data from server to ensure consistency
      await loadTeamData();

      Alert.alert('Success', `${employee.name} has been added to your team.`);
    } catch (error) {
      console.error('Error adding team member:', error);
      Alert.alert('Error', 'Failed to add team member. Please try again.');
    }
  };

  const removeFromTeam = async (employee: Employee) => {
    Alert.alert(
      'Remove Team Member',
      `Are you sure you want to remove ${employee.name} from your team?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await teamService.removeEmployeeFromManager(employee.id);

              // Refresh data from server to ensure consistency
              await loadTeamData();

              Alert.alert('Success', `${employee.name} has been removed from your team.`);
            } catch (error) {
              console.error('Error removing team member:', error);
              Alert.alert('Error', 'Failed to remove team member. Please try again.');
            }
          }
        }
      ]
    );
  };

  const filteredAvailableEmployees = availableEmployees.filter(emp =>
    emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    emp.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    emp.department?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading team data...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <Card style={styles.headerCard}>
        <Card.Content>
          <View style={styles.headerContent}>
            <MaterialCommunityIcons name="account-group" size={32} color="#1e3a8a" />
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>Team Management</Text>
              <Text style={styles.headerSubtitle}>
                Manage your team members and assignments
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Current Team Members */}
      <Card style={styles.sectionCard}>
        <Card.Content>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>My Team ({teamMembers.length})</Text>
            <Button
              mode="outlined"
              onPress={() => setShowAddMembers(!showAddMembers)}
              icon={showAddMembers ? "minus" : "plus"}
              style={{ minWidth: 120 }}
            >
              {showAddMembers ? "Cancel" : "Add Members"}
            </Button>
          </View>

          {teamMembers.length === 0 ? (
            <View style={styles.emptyState}>
              <MaterialCommunityIcons name="account-off" size={48} color="#ccc" />
              <Text style={styles.emptyStateText}>No team members assigned</Text>
              <Text style={styles.emptyStateSubtext}>
                Add employees to your team to get started
              </Text>
            </View>
          ) : (
            teamMembers.map((member) => (
              <List.Item
                key={member.id}
                title={member.name}
                description={`${member.email} • ${member.department}`}
                left={() => (
                  <Avatar.Text 
                    size={40} 
                    label={member.name.split(' ').map(n => n[0]).join('')}
                    style={styles.avatar}
                  />
                )}
                right={() => (
                  <IconButton
                    icon="account-minus"
                    iconColor="#ef4444"
                    onPress={() => removeFromTeam(member)}
                  />
                )}
                style={styles.listItem}
              />
            ))
          )}
        </Card.Content>
      </Card>

      {/* Add Team Members Section */}
      {showAddMembers && (
        <Card style={styles.sectionCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Add Team Members</Text>
            
            <Searchbar
              placeholder="Search employees..."
              onChangeText={setSearchQuery}
              value={searchQuery}
              style={styles.searchBar}
              icon="account-search"
            />

            {filteredAvailableEmployees.length === 0 ? (
              <View style={styles.emptyState}>
                <MaterialCommunityIcons name="account-search" size={48} color="#ccc" />
                <Text style={styles.emptyStateText}>
                  {searchQuery ? 'No employees found' : 'No available employees'}
                </Text>
                <Text style={styles.emptyStateSubtext}>
                  {searchQuery ? 'Try a different search term' : 'All employees are already assigned'}
                </Text>
              </View>
            ) : (
              filteredAvailableEmployees.map((employee) => (
                <List.Item
                  key={employee.id}
                  title={employee.name}
                  description={`${employee.email} • ${employee.department}`}
                  left={() => (
                    <Avatar.Text 
                      size={40} 
                      label={employee.name.split(' ').map(n => n[0]).join('')}
                      style={styles.avatar}
                    />
                  )}
                  right={() => (
                    <Button
                      mode="contained"
                      onPress={() => addToTeam(employee)}
                      icon="account-plus"
                      style={{ minWidth: 80 }}
                    >
                      Add
                    </Button>
                  )}
                  style={styles.listItem}
                />
              ))
            )}
          </Card.Content>
        </Card>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  headerCard: {
    marginBottom: 16,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    marginLeft: 16,
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e3a8a',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  sectionCard: {
    marginBottom: 16,
    elevation: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  listItem: {
    paddingVertical: 8,
  },
  avatar: {
    backgroundColor: '#1e3a8a',
  },
  searchBar: {
    marginBottom: 16,
  },
});

export default TeamManagementScreen;
