export interface Project {
  id: string;
  name: string;
  description?: string;
  location?: string;
  createdBy: string; // User ID of creator (usually superadmin)
  createdAt: Date;
  status: 'active' | 'completed' | 'on-hold' | 'cancelled';
  teamIds?: string[]; // Optional: specific teams assigned to project
}

export interface ProjectUpdate {
  id: string;
  projectId: string;
  projectName: string; // Denormalized for easier display
  heading: string;
  description: string;
  imageUrl?: string;
  imageName?: string;
  createdBy: string; // User ID of creator
  createdByName: string; // Denormalized for easier display
  createdByRole: 'manager' | 'superadmin';
  createdAt: Date;
  updatedAt: Date;
  
  // Visibility and access control
  visibleToTeamIds?: string[]; // For manager posts - which teams can see this
  visibleToAll: boolean; // For superadmin posts - visible to everyone
  managerId?: string; // If created by manager, store their ID for team filtering
}

export interface ProjectUpdateFormData {
  projectId: string;
  heading: string;
  description: string;
  image?: {
    uri: string;
    name: string;
    type: string;
  };
}

export interface ProjectUpdateFilters {
  projectId?: string;
  createdBy?: string;
  userRole: 'employee' | 'manager' | 'superadmin';
  userId: string;
  managerId?: string; // For employees - their manager's ID
}

// For creating new projects (Super Admin only)
export interface CreateProjectData {
  name: string;
  description?: string;
  location?: string;
  status?: 'active' | 'completed' | 'on-hold' | 'cancelled';
  teamIds?: string[];
}

// Response types for API calls
export interface ProjectUpdatesResponse {
  updates: ProjectUpdate[];
  hasMore: boolean;
  lastDoc?: any; // For pagination
}

export interface ProjectsResponse {
  projects: Project[];
  hasMore: boolean;
}

// Visibility rules helper type
export interface VisibilityRules {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  reason?: string; // For debugging/logging
}

// Update visibility based on user role and relationship
export const getUpdateVisibility = (
  update: ProjectUpdate,
  currentUser: {
    id: string;
    role: 'employee' | 'manager' | 'superadmin';
    managerId?: string;
  }
): VisibilityRules => {
  const { role, id: userId, managerId } = currentUser;
  
  // Super admin can see and manage everything
  if (role === 'superadmin') {
    return {
      canView: true,
      canCreate: true,
      canEdit: update.createdBy === userId,
      canDelete: true, // Superadmin can delete any update
    };
  }
  
  // Manager can see their own updates and superadmin updates
  if (role === 'manager') {
    const canViewUpdate = 
      update.visibleToAll || // Superadmin posts
      update.createdBy === userId; // Their own posts
      
    return {
      canView: canViewUpdate,
      canCreate: true,
      canEdit: update.createdBy === userId,
      canDelete: update.createdBy === userId,
    };
  }
  
  // Employee can see updates from their manager and superadmin
  if (role === 'employee') {
    const canViewUpdate = 
      update.visibleToAll || // Superadmin posts
      (update.managerId === managerId); // Posts from their manager
      
    return {
      canView: canViewUpdate,
      canCreate: false,
      canEdit: false,
      canDelete: false,
    };
  }
  
  // Default: no access
  return {
    canView: false,
    canCreate: false,
    canEdit: false,
    canDelete: false,
    reason: 'Unknown user role or insufficient permissions',
  };
};

// Helper function to determine update visibility scope when creating
export const getUpdateScope = (
  creatorRole: 'manager' | 'superadmin',
  creatorId: string
): Partial<Pick<ProjectUpdate, 'visibleToAll' | 'managerId'>> => {
  if (creatorRole === 'superadmin') {
    return {
      visibleToAll: true,
      // Don't include managerId for superadmins (undefined values not allowed in Firestore)
    };
  }

  if (creatorRole === 'manager') {
    return {
      visibleToAll: false,
      managerId: creatorId,
    };
  }

  throw new Error('Invalid creator role for project update');
};
