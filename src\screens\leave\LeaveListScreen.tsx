import React, { useEffect, useState, useCallback } from 'react';
import { View, FlatList, ScrollView } from 'react-native';
import { Text, Button, Card, ActivityIndicator, FAB, Chip, Divider } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import { useFocusEffect } from '@react-navigation/native';

interface LeaveRequest {
  id: string;
  userId: string;
  userEmail: string;
  userName: string;
  userDepartment: string;
  fromDate: string;
  toDate: string;
  leaveType: string;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  totalDays: number;
  createdAt: string;
  updatedAt: string;
  approverId?: string;
  approverName?: string;
  approvedAt?: string;
}

const LeaveListScreen = ({ navigation }: any) => {
  const [leaves, setLeaves] = useState<LeaveRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [userRole, setUserRole] = useState('employee');
  const [userDepartment, setUserDepartment] = useState('');
  const [userId, setUserId] = useState('');
  const [currentUserProfile, setCurrentUserProfile] = useState<any>(null);

  const fetchUserProfile = useCallback(async () => {
    const currentUser = supabaseAuthService.getCurrentUser();
    if (!currentUser) return;

    setUserId(currentUser.id);

    try {
      const userData = await db.users.getById(currentUser.id);
      if (userData) {
        setCurrentUserProfile(userData);
        setUserRole(userData.role || 'employee');
        setUserDepartment(userData.department || '');
        console.log('User role:', userData.role, 'Department:', userData.department);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  }, []);

  const fetchLeaves = useCallback(async () => {
    setLoading(true);
    try {
      const data = await db.leaves.getAll();
      console.log('Fetched leaves:', data.length);
      console.log('All leaves data:', data);

      // Transform Supabase data to match our interface
      const transformedLeaves = data.map((leave: any) => ({
        id: leave.id,
        userId: leave.user_id,
        userEmail: leave.user_email,
        userName: leave.user_name,
        userDepartment: leave.user_department,
        fromDate: leave.from_date,
        toDate: leave.to_date,
        leaveType: leave.leave_type,
        reason: leave.reason,
        status: leave.status,
        appliedDate: leave.applied_date,
        approvedBy: leave.approved_by,
        approverName: leave.approver_name,
      }));

      setLeaves(transformedLeaves);
    } catch (e: any) {
      console.error('Error fetching leaves:', e);
      setError(e.message);
      console.error('Error fetching leaves:', e);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleApprove = async (leaveId: string, leaveData: LeaveRequest) => {
    try {
      const approverName = currentUserProfile?.name || 'Unknown';
      await db.leaves.update(leaveId, {
        status: 'approved',
        approved_by: userId,
        approver_name: approverName,
        approved_date: new Date().toISOString(),
      });
      fetchLeaves();
    } catch (error) {
      console.error('Error approving leave:', error);
    }
  };

  const handleReject = async (leaveId: string, leaveData: LeaveRequest) => {
    try {
      const approverName = currentUserProfile?.name || 'Unknown';
      await db.leaves.update(leaveId, {
        status: 'rejected',
        approved_by: userId,
        approver_name: approverName,
        approved_date: new Date().toISOString(),
      });
      fetchLeaves();
    } catch (error) {
      console.error('Error rejecting leave:', error);
    }
  };

  useEffect(() => {
    fetchUserProfile();
  }, [fetchUserProfile]);

  useEffect(() => {
    if (userId) {
      fetchLeaves();
    }
  }, [userId, fetchLeaves]);

  // Refresh when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      if (userId) {
        fetchLeaves();
      }
    }, [userId, fetchLeaves])
  );

  // Filter leaves based on role
  const filteredLeaves = leaves.filter(leave => {
    console.log('Filtering leave:', {
      leaveId: leave.id,
      leaveUserId: leave.userId,
      leaveUserDepartment: leave.userDepartment,
      currentUserId: userId,
      currentUserRole: userRole,
      currentUserDepartment: userDepartment
    });

    if (userRole === 'superadmin') {
      // Superadmin can see all leaves
      console.log('Superadmin - showing all leaves');
      return true;
    }
    if (userRole === 'manager') {
      // Manager can see leaves from their department + their own leaves
      const canSee = leave.userDepartment === userDepartment || leave.userId === userId;
      console.log('Manager - can see this leave:', canSee);
      return canSee;
    }
    // Employee can only see their own leaves
    const canSee = leave.userId === userId;
    console.log('Employee - can see this leave:', canSee);
    return canSee;
  });

  // Separate leaves by status for better organization
  const pendingLeaves = filteredLeaves.filter(l => l.status === 'pending');
  const approvedLeaves = filteredLeaves.filter(l => l.status === 'approved');
  const rejectedLeaves = filteredLeaves.filter(l => l.status === 'rejected');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f59e0b';
      case 'approved': return '#10b981';
      case 'rejected': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const canApproveReject = (leave: LeaveRequest) => {
    return (userRole === 'superadmin' || userRole === 'manager') &&
           leave.status === 'pending' &&
           leave.userId !== userId; // Can't approve own leave
  };

  if (loading) return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" />
      <Text style={{ marginTop: 16 }}>Loading leave requests...</Text>
    </View>
  );

  const renderLeaveCard = ({ item }: { item: LeaveRequest }) => (
    <Card style={{ marginVertical: 8, elevation: 2 }}>
      <Card.Content>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 12 }}>
          <View style={{ flex: 1 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#1e3a8a' }}>
              {item.userName}
            </Text>
            <Text style={{ color: '#6b7280', fontSize: 13 }}>
              {item.userEmail} • {item.userDepartment || 'No Department'}
            </Text>
          </View>
          <Chip
            mode="flat"
            style={{ backgroundColor: getStatusColor(item.status) + '20' }}
            textStyle={{ color: getStatusColor(item.status), fontWeight: '500' }}
          >
            {item.status.toUpperCase()}
          </Chip>
        </View>

        <View style={{ backgroundColor: '#f8fafc', padding: 12, borderRadius: 8, marginBottom: 12 }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
            <Text style={{ fontWeight: '500', color: '#374151' }}>
              {item.leaveType.charAt(0).toUpperCase() + item.leaveType.slice(1)} Leave
            </Text>
            <Text style={{ color: '#6b7280', fontSize: 13 }}>
              {item.totalDays} day{item.totalDays !== 1 ? 's' : ''}
            </Text>
          </View>
          <Text style={{ color: '#6b7280', fontSize: 13 }}>
            {item.fromDate} to {item.toDate}
          </Text>
        </View>

        <Text style={{ color: '#374151', marginBottom: 12 }}>
          <Text style={{ fontWeight: '500' }}>Reason: </Text>
          {item.reason}
        </Text>

        <Text style={{ color: '#9ca3af', fontSize: 12 }}>
          Applied on {new Date(item.createdAt).toLocaleDateString()}
        </Text>
      </Card.Content>

      {canApproveReject(item) && (
        <>
          <Divider />
          <Card.Actions style={{ justifyContent: 'flex-end' }}>
            <Button
              mode="outlined"
              onPress={() => handleReject(item.id, item)}
              textColor="#ef4444"
              style={{ borderColor: '#ef4444' }}
            >
              Reject
            </Button>
            <Button
              mode="contained"
              onPress={() => handleApprove(item.id, item)}
              buttonColor="#10b981"
            >
              Approve
            </Button>
          </Card.Actions>
        </>
      )}
    </Card>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 16 }}>
        {/* Header */}
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#1e3a8a', marginBottom: 8 }}>
            Leave Management
          </Text>
          <Text style={{ color: '#6b7280', marginBottom: 16 }}>
            {userRole === 'superadmin' ? 'All leave requests' :
             userRole === 'manager' ? `${userDepartment} department requests` :
             'Your leave requests'}
          </Text>

          <Button
            mode="contained"
            onPress={() => navigation.navigate('LeaveApply')}
            style={{ marginBottom: 16 }}
          >
            Apply for Leave
          </Button>
        </View>

        {/* Pending Leaves */}
        {pendingLeaves.length > 0 && (
          <View style={{ marginBottom: 24 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#f59e0b', marginBottom: 12 }}>
              Pending Requests ({pendingLeaves.length})
            </Text>
            {pendingLeaves.map(item => (
              <View key={item.id}>
                {renderLeaveCard({ item })}
              </View>
            ))}
          </View>
        )}

        {/* Approved Leaves */}
        {approvedLeaves.length > 0 && (
          <View style={{ marginBottom: 24 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#10b981', marginBottom: 12 }}>
              Approved Requests ({approvedLeaves.length})
            </Text>
            {approvedLeaves.map(item => (
              <View key={item.id}>
                {renderLeaveCard({ item })}
              </View>
            ))}
          </View>
        )}

        {/* Rejected Leaves */}
        {rejectedLeaves.length > 0 && (
          <View style={{ marginBottom: 24 }}>
            <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#ef4444', marginBottom: 12 }}>
              Rejected Requests ({rejectedLeaves.length})
            </Text>
            {rejectedLeaves.map(item => (
              <View key={item.id}>
                {renderLeaveCard({ item })}
              </View>
            ))}
          </View>
        )}

        {/* Empty State */}
        {filteredLeaves.length === 0 && (
          <View style={{ alignItems: 'center', marginTop: 64 }}>
            <Text style={{ fontSize: 16, color: '#6b7280', textAlign: 'center' }}>
              No leave requests found
            </Text>
            <Text style={{ color: '#9ca3af', textAlign: 'center', marginTop: 8 }}>
              {userRole === 'employee' ?
                "You haven't applied for any leave yet" :
                "No leave requests to review"}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default LeaveListScreen; 