import { db } from './firebase';
import { 
  collection, 
  doc, 
  getDocs, 
  updateDoc, 
  query, 
  where, 
  orderBy,
  getDoc 
} from 'firebase/firestore';

export interface Employee {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  managerId?: string;
  profilePicture?: string;
  phone?: string;
  position?: string;
}

class TeamService {
  // Get all team members for a specific manager
  async getTeamMembers(managerId: string): Promise<Employee[]> {
    try {
      const usersRef = collection(db, 'users');
      // Simplified query - only filter by managerId, then filter role in memory
      const q = query(usersRef, where('managerId', '==', managerId));

      const querySnapshot = await getDocs(q);
      const teamMembers: Employee[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        // Filter by role in memory to avoid composite index requirement
        if (data.role === 'employee') {
          teamMembers.push({
            id: doc.id,
            name: data.name || 'Unknown',
            email: data.email || '',
            role: data.role || 'employee',
            department: data.department,
            managerId: data.managerId,
            profilePicture: data.profilePicture,
            phone: data.phone,
            position: data.position,
          });
        }
      });

      // Sort by name in memory
      teamMembers.sort((a, b) => a.name.localeCompare(b.name));

      return teamMembers;
    } catch (error) {
      console.error('Error fetching team members:', error);
      throw new Error('Failed to fetch team members');
    }
  }

  // Get all available employees (not assigned to any manager)
  async getAvailableEmployees(): Promise<Employee[]> {
    try {
      const usersRef = collection(db, 'users');
      // Simplified query - only filter by role
      const q = query(usersRef, where('role', '==', 'employee'));

      const querySnapshot = await getDocs(q);
      const availableEmployees: Employee[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        // Only include employees without a manager
        if (!data.managerId) {
          availableEmployees.push({
            id: doc.id,
            name: data.name || 'Unknown',
            email: data.email || '',
            role: data.role || 'employee',
            department: data.department,
            managerId: data.managerId,
            profilePicture: data.profilePicture,
            phone: data.phone,
            position: data.position,
          });
        }
      });

      // Sort by name in memory
      availableEmployees.sort((a, b) => a.name.localeCompare(b.name));

      return availableEmployees;
    } catch (error) {
      console.error('Error fetching available employees:', error);
      throw new Error('Failed to fetch available employees');
    }
  }

  // Assign an employee to a manager
  async assignEmployeeToManager(employeeId: string, managerId: string): Promise<void> {
    try {
      const employeeRef = doc(db, 'users', employeeId);
      await updateDoc(employeeRef, {
        managerId: managerId
      });
    } catch (error) {
      console.error('Error assigning employee to manager:', error);
      throw new Error('Failed to assign employee to manager');
    }
  }

  // Remove an employee from a manager (unassign)
  async removeEmployeeFromManager(employeeId: string): Promise<void> {
    try {
      const employeeRef = doc(db, 'users', employeeId);
      await updateDoc(employeeRef, {
        managerId: null
      });
    } catch (error) {
      console.error('Error removing employee from manager:', error);
      throw new Error('Failed to remove employee from manager');
    }
  }

  // Get all employees (for super admin)
  async getAllEmployees(): Promise<Employee[]> {
    try {
      const usersRef = collection(db, 'users');
      // Simplified query - only filter by role
      const q = query(usersRef, where('role', '==', 'employee'));

      const querySnapshot = await getDocs(q);
      const employees: Employee[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        employees.push({
          id: doc.id,
          name: data.name || 'Unknown',
          email: data.email || '',
          role: data.role || 'employee',
          department: data.department,
          managerId: data.managerId,
          profilePicture: data.profilePicture,
          phone: data.phone,
          position: data.position,
        });
      });

      // Sort by name in memory
      employees.sort((a, b) => a.name.localeCompare(b.name));

      return employees;
    } catch (error) {
      console.error('Error fetching all employees:', error);
      throw new Error('Failed to fetch all employees');
    }
  }

  // Get all managers (for super admin)
  async getAllManagers(): Promise<Employee[]> {
    try {
      const usersRef = collection(db, 'users');
      // Simplified query - only filter by role
      const q = query(usersRef, where('role', '==', 'manager'));

      const querySnapshot = await getDocs(q);
      const managers: Employee[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        managers.push({
          id: doc.id,
          name: data.name || 'Unknown',
          email: data.email || '',
          role: data.role || 'manager',
          department: data.department,
          managerId: data.managerId,
          profilePicture: data.profilePicture,
          phone: data.phone,
          position: data.position,
        });
      });

      // Sort by name in memory
      managers.sort((a, b) => a.name.localeCompare(b.name));

      return managers;
    } catch (error) {
      console.error('Error fetching all managers:', error);
      throw new Error('Failed to fetch all managers');
    }
  }

  // Get employee details by ID
  async getEmployeeById(employeeId: string): Promise<Employee | null> {
    try {
      const employeeRef = doc(db, 'users', employeeId);
      const employeeDoc = await getDoc(employeeRef);
      
      if (employeeDoc.exists()) {
        const data = employeeDoc.data();
        return {
          id: employeeDoc.id,
          name: data.name || 'Unknown',
          email: data.email || '',
          role: data.role || 'employee',
          department: data.department,
          managerId: data.managerId,
          profilePicture: data.profilePicture,
          phone: data.phone,
          position: data.position,
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching employee by ID:', error);
      throw new Error('Failed to fetch employee details');
    }
  }

  // Search employees by name, email, or department
  async searchEmployees(searchTerm: string, managerId?: string): Promise<Employee[]> {
    try {
      const usersRef = collection(db, 'users');
      let q = query(
        usersRef,
        where('role', '==', 'employee'),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      const employees: Employee[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const employee: Employee = {
          id: doc.id,
          name: data.name || 'Unknown',
          email: data.email || '',
          role: data.role || 'employee',
          department: data.department,
          managerId: data.managerId,
          profilePicture: data.profilePicture,
          phone: data.phone,
          position: data.position,
        };
        
        // Filter by search term
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch = 
          employee.name.toLowerCase().includes(searchLower) ||
          employee.email.toLowerCase().includes(searchLower) ||
          (employee.department && employee.department.toLowerCase().includes(searchLower));
        
        // Filter by manager if specified
        const matchesManager = managerId ? employee.managerId === managerId : true;
        
        if (matchesSearch && matchesManager) {
          employees.push(employee);
        }
      });
      
      return employees;
    } catch (error) {
      console.error('Error searching employees:', error);
      throw new Error('Failed to search employees');
    }
  }
}

export const teamService = new TeamService();
