import { makeRedirectUri } from 'expo-auth-session';
import * as <PERSON><PERSON>rowser from 'expo-web-browser';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Complete the auth session for web browsers
WebBrowser.maybeCompleteAuthSession();

// Simple user interface
export interface User {
  id: string;
  email: string;
  name?: string;
  role?: string;
  managerId?: string;
}

// Auth state management
class AuthService {
  private currentUser: User | null = null;
  private listeners: ((user: User | null) => void)[] = [];

  // Get current user
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  // Update current user with additional data (like role)
  updateCurrentUser(updates: Partial<User>): void {
    if (this.currentUser) {
      this.currentUser = { ...this.currentUser, ...updates };
      // Also update in AsyncStorage
      AsyncStorage.setItem('user', JSON.stringify(this.currentUser));
      // Don't call notifyListeners() to avoid infinite loops
      // The auth listener that called this already has the updated user
    }
  }

  // Subscribe to auth state changes
  onAuthStateChanged(callback: (user: User | null) => void): () => void {
    this.listeners.push(callback);

    // Immediately call with current state after a small delay to avoid blocking
    setTimeout(() => {
      callback(this.currentUser);
    }, 100);

    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Notify all listeners of auth state change
  private notifyListeners() {
    this.listeners.forEach(callback => callback(this.currentUser));
  }

  // Generate consistent user ID based on email
  private generateConsistentUserId(email: string): string {
    // Simple hash function to create consistent ID from email
    let hash = 0;
    for (let i = 0; i < email.length; i++) {
      const char = email.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `user_${Math.abs(hash)}`;
  }

  // Initialize auth service (check for stored session)
  async initialize(): Promise<void> {
    try {
      console.log('Initializing auth service...');
      const storedUser = await AsyncStorage.getItem('user');
      if (storedUser) {
        console.log('Found stored user, restoring session');
        this.currentUser = JSON.parse(storedUser);
        this.notifyListeners();
      } else {
        console.log('No stored user found');
        this.notifyListeners(); // Still notify to trigger initial state
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      this.notifyListeners(); // Notify even on error to prevent infinite loading
    }
  }

  // Simple email/password sign in (for demo purposes)
  async signInWithEmailAndPassword(email: string, password: string): Promise<User> {
    try {
      console.log('Attempting sign in for:', email);
      // For demo purposes, we'll create a simple user object
      // In a real app, you'd validate credentials with your backend
      if (email && password.length >= 6) {
        // Create consistent user ID based on email to ensure data persistence
        const userId = this.generateConsistentUserId(email);
        const user: User = {
          id: userId,
          email: email,
          name: email.split('@')[0]
        };

        console.log('Sign in successful, storing user:', user);
        this.currentUser = user;
        await AsyncStorage.setItem('user', JSON.stringify(user));
        this.notifyListeners();
        return user;
      } else {
        throw new Error('Invalid email or password');
      }
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  // Simple email/password sign up (for demo purposes)
  async createUserWithEmailAndPassword(email: string, password: string, customId?: string): Promise<User> {
    try {
      // For demo purposes, we'll create a simple user object
      // In a real app, you'd create the user in your backend
      if (email && password.length >= 6) {
        // Use custom ID if provided (from Supabase), otherwise generate one
        const userId = customId || this.generateConsistentUserId(email);
        const user: User = {
          id: userId,
          email: email,
          name: email.split('@')[0]
        };

        this.currentUser = user;
        await AsyncStorage.setItem('user', JSON.stringify(user));
        this.notifyListeners();
        return user;
      } else {
        throw new Error('Invalid email or password (minimum 6 characters)');
      }
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      this.currentUser = null;
      await AsyncStorage.removeItem('user');
      this.notifyListeners();
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  // Google Sign In using Expo AuthSession (for future implementation)
  async signInWithGoogle(): Promise<User> {
    // This would implement Google OAuth using expo-auth-session
    // For now, we'll throw an error to indicate it's not implemented
    throw new Error('Google Sign In not implemented yet');
  }
}

// Export singleton instance
export const authService = new AuthService();

// Initialize the auth service (don't await here to avoid blocking)
authService.initialize().catch(console.error);
