# Supabase Migration Guide

## ✅ Completed Steps

1. **✅ Installed Supabase client** - `@supabase/supabase-js`
2. **✅ Created Supabase configuration** - `src/services/supabase.ts`
3. **✅ Created database schema** - `supabase-schema.sql`
4. **✅ Updated image upload service** - Now uses Supabase Storage
5. **✅ Updated project updates service** - Now uses Supabase Database
6. **✅ Updated key screen imports** - AppNavigator, SignupScreen, etc.

## 🎯 Next Steps Required

### Step 1: Set Up Database Schema
1. Go to your Supabase Dashboard: https://supabase.com/dashboard/project/nmmpfufiiaqspbxrqocn
2. Click "SQL Editor" in the left sidebar
3. Click "New Query"
4. Copy and paste the entire content from `supabase-schema.sql`
5. Click "Run" to execute the SQL

### Step 2: Enable Storage
1. In Supabase Dashboard, go to "Storage"
2. The schema script should have created an "images" bucket
3. Verify the bucket exists and is public

### Step 3: Test the Migration
1. Reload your app in Expo Go
2. Try creating a new user account
3. Try creating project updates with images
4. Verify data is being saved to Supabase

## 🔧 What Changed

### Database Structure
- **Firebase Collections** → **Supabase Tables**
- `users` collection → `users` table
- `projects` collection → `projects` table  
- `projectUpdates` collection → `project_updates` table
- `leaves` collection → `leaves` table
- `announcements` collection → `announcements` table

### Field Name Changes
- `createdBy` → `created_by`
- `createdAt` → `created_at`
- `updatedAt` → `updated_at`
- `projectId` → `project_id`
- `managerId` → `manager_id`
- `photoURL` → `photo_url`
- `imageUrl` → `image_url`

### Storage
- **Firebase Storage** → **Supabase Storage**
- Images now stored in `images` bucket
- Public URLs automatically generated
- No billing restrictions!

## 🎉 Benefits Gained

1. **✅ Free Storage** - 1GB included in free tier
2. **✅ PostgreSQL Database** - More powerful than Firestore
3. **✅ Real-time subscriptions** - Better than Firestore
4. **✅ Row Level Security** - Better security model
5. **✅ No billing upgrade required** - Everything works on free tier

## 🚨 Files Still Using Firebase

The following files may still need updates (check if they exist and are used):

- `src/screens/dashboard/EditProfileScreen.tsx`
- `src/screens/dashboard/ManagerDashboard.tsx`
- `src/screens/directory/DirectoryScreen.tsx`
- `src/screens/admin/AdminPanelScreen.tsx`
- `src/screens/announcements/AnnouncementsScreen.tsx`
- `src/screens/leaves/LeaveApplicationScreen.tsx`
- `src/screens/leaves/LeaveManagementScreen.tsx`

These will need their imports updated from:
```typescript
import { db } from '../../services/firebase';
import { collection, getDocs, ... } from 'firebase/firestore';
```

To:
```typescript
import { db } from '../../services/supabase';
```

And their database operations updated to use the new Supabase helper functions.

## 🔄 Rollback Plan

If you need to rollback to Firebase:
1. Keep the `src/services/firebase.ts` file
2. Change imports back to `'./firebase'`
3. The data structures are compatible

## 📞 Support

After running the SQL schema and testing:
- If you encounter any issues, let me know the specific error messages
- We can debug and fix any remaining compatibility issues
- The migration should be seamless once the database schema is set up
