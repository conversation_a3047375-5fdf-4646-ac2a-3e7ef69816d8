import React, { useEffect, useState } from 'react';
import { View, ScrollView, Image } from 'react-native';
import { Text, Card, ActivityIndicator } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db, supabase } from '../../services/supabase';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  phone: string;
  photoURL: string;
}

const LOGO = require('../../../assets/texam-logo.png');

const ManagerDashboard = ({ navigation }: any) => {
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState({
    teamSize: 0,
    teamLeaves: 0,
    teamProjects: 0,
    pendingApprovals: 0,
    myAnnouncements: 0,
    departmentSize: 0,
  });

  const fetchStats = async () => {
    setLoading(true);
    try {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;

      // Get current user profile
      const userProfile = await db.users.getById(currentUser.id);
      if (!userProfile) return;

      // Fetch team members (users in same department)
      const allUsers = await db.users.getAll();
      const teamMembers = allUsers.filter((user: any) => 
        user.department === userProfile.department && user.role === 'employee'
      );
      const departmentMembers = allUsers.filter((user: any) => 
        user.department === userProfile.department
      );

      // Fetch team leaves (if available)
      let teamLeaves = 0;
      try {
        const { data: leavesData } = await supabase
          .from('leave_applications')
          .select('id')
          .in('user_id', teamMembers.map((member: any) => member.id));
        teamLeaves = leavesData?.length || 0;
      } catch (error) {
        console.log('Leave applications table not available yet');
      }

      // Fetch pending approvals (if available)
      let pendingApprovals = 0;
      try {
        const { data: pendingData } = await supabase
          .from('leave_applications')
          .select('id')
          .eq('status', 'pending')
          .in('user_id', teamMembers.map((member: any) => member.id));
        pendingApprovals = pendingData?.length || 0;
      } catch (error) {
        console.log('Leave applications table not available yet');
      }

      // Fetch team projects (if available)
      let teamProjects = 0;
      try {
        const { data: projectsData } = await supabase
          .from('projects')
          .select('id')
          .eq('status', 'active');
        teamProjects = projectsData?.length || 0;
      } catch (error) {
        console.log('Projects table not available yet');
      }

      // Fetch my announcements (if available)
      let myAnnouncements = 0;
      try {
        const { data: announcementsData } = await supabase
          .from('announcements')
          .select('id')
          .eq('created_by', currentUser.id);
        myAnnouncements = announcementsData?.length || 0;
      } catch (error) {
        console.log('Announcements table not available yet');
      }

      setStats({
        teamSize: teamMembers.length,
        teamLeaves,
        teamProjects,
        pendingApprovals,
        myAnnouncements,
        departmentSize: departmentMembers.length,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  useEffect(() => {
    const fetchProfile = async () => {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;
      try {
        const userData = await db.users.getById(currentUser.id);
        if (userData) {
          setProfile({
            id: userData.id,
            name: userData.name || '',
            email: userData.email || '',
            role: userData.role || '',
            department: userData.department || '',
            phone: userData.phone || '',
            photoURL: userData.photoURL || '',
          });
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };
    fetchProfile();
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 16 }}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      {/* Header */}
      <View style={{ 
        backgroundColor: '#1e3a8a', 
        padding: 20, 
        paddingTop: 40,
        borderBottomLeftRadius: 20,
        borderBottomRightRadius: 20
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <Image source={LOGO} style={{ width: 40, height: 40, marginRight: 12 }} />
          <View style={{ flex: 1 }}>
            <Text style={{ color: '#fff', fontSize: 24, fontWeight: 'bold' }}>
              Manager Dashboard
            </Text>
            <Text style={{ color: '#bfdbfe', fontSize: 14 }}>
              Welcome back, {profile?.name || 'Manager'}
            </Text>
          </View>
        </View>
      </View>

      {/* Team Overview */}
      <View style={{ padding: 16 }}>
        <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 16, color: '#333' }}>
          Team Overview
        </Text>
        
        {/* First Row */}
        <View style={{ flexDirection: 'row', marginBottom: 12 }}>
          <Card style={{ flex: 1, marginRight: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="account-group" size={32} color="#1e3a8a" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1e3a8a', marginTop: 8 }}>
                {stats.teamSize}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Team Members
              </Text>
            </Card.Content>
          </Card>
          
          <Card style={{ flex: 1, marginLeft: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="domain" size={32} color="#16a34a" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#16a34a', marginTop: 8 }}>
                {stats.departmentSize}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Department Size
              </Text>
            </Card.Content>
          </Card>
        </View>

        {/* Second Row */}
        <View style={{ flexDirection: 'row', marginBottom: 12 }}>
          <Card style={{ flex: 1, marginRight: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="calendar-clock" size={32} color="#ea580c" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#ea580c', marginTop: 8 }}>
                {stats.pendingApprovals}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Pending Approvals
              </Text>
            </Card.Content>
          </Card>
          
          <Card style={{ flex: 1, marginLeft: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="calendar-check" size={32} color="#dc2626" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#dc2626', marginTop: 8 }}>
                {stats.teamLeaves}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Team Leaves
              </Text>
            </Card.Content>
          </Card>
        </View>

        {/* Third Row */}
        <View style={{ flexDirection: 'row', marginBottom: 16 }}>
          <Card style={{ flex: 1, marginRight: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="folder-multiple" size={32} color="#059669" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#059669', marginTop: 8 }}>
                {stats.teamProjects}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                Active Projects
              </Text>
            </Card.Content>
          </Card>
          
          <Card style={{ flex: 1, marginLeft: 6, elevation: 2 }}>
            <Card.Content style={{ alignItems: 'center', padding: 16 }}>
              <MaterialCommunityIcons name="bullhorn" size={32} color="#7c3aed" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#7c3aed', marginTop: 8 }}>
                {stats.myAnnouncements}
              </Text>
              <Text style={{ fontSize: 12, color: '#666', textAlign: 'center' }}>
                My Announcements
              </Text>
            </Card.Content>
          </Card>
        </View>


      </View>
    </ScrollView>
  );
};

export default ManagerDashboard;
