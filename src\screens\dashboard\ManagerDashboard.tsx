import React, { useEffect, useState } from 'react';
import { View, ScrollView, Image } from 'react-native';
import { Text, Card, ActivityIndicator } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db, supabase } from '../../services/supabase';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  phone: string;
  photoURL: string;
}

const LOGO = require('../../../assets/texam-logo.png');

const ManagerDashboard = ({ navigation }: any) => {
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState({
    teamSize: 0,
    teamLeaves: 0,
    teamProjects: 0,
    pendingApprovals: 0,
    myAnnouncements: 0,
    departmentSize: 0,
  });

  const fetchStats = async () => {
    setLoading(true);
    try {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;

      // Get current user profile
      const userProfile = await db.users.getById(currentUser.id);
      if (!userProfile) return;

      // Fetch team members (users in same department)
      const allUsers = await db.users.getAll();
      const teamMembers = allUsers.filter((user: any) => 
        user.department === userProfile.department && user.role === 'employee'
      );
      const departmentMembers = allUsers.filter((user: any) => 
        user.department === userProfile.department
      );

      // Fetch team leaves (if available)
      let teamLeaves = 0;
      try {
        const { data: leavesData } = await supabase
          .from('leave_applications')
          .select('id')
          .in('user_id', teamMembers.map((member: any) => member.id));
        teamLeaves = leavesData?.length || 0;
      } catch (error) {
        console.log('Leave applications table not available yet');
      }

      // Fetch pending approvals (if available)
      let pendingApprovals = 0;
      try {
        const { data: pendingData } = await supabase
          .from('leave_applications')
          .select('id')
          .eq('status', 'pending')
          .in('user_id', teamMembers.map((member: any) => member.id));
        pendingApprovals = pendingData?.length || 0;
      } catch (error) {
        console.log('Leave applications table not available yet');
      }

      // Fetch team projects (if available)
      let teamProjects = 0;
      try {
        const { data: projectsData } = await supabase
          .from('projects')
          .select('id')
          .eq('status', 'active');
        teamProjects = projectsData?.length || 0;
      } catch (error) {
        console.log('Projects table not available yet');
      }

      // Fetch my announcements (if available)
      let myAnnouncements = 0;
      try {
        const { data: announcementsData } = await supabase
          .from('announcements')
          .select('id')
          .eq('created_by', currentUser.id);
        myAnnouncements = announcementsData?.length || 0;
      } catch (error) {
        console.log('Announcements table not available yet');
      }

      setStats({
        teamSize: teamMembers.length,
        teamLeaves,
        teamProjects,
        pendingApprovals,
        myAnnouncements,
        departmentSize: departmentMembers.length,
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  useEffect(() => {
    const fetchProfile = async () => {
      const currentUser = supabaseAuthService.getCurrentUser();
      if (!currentUser) return;
      try {
        const userData = await db.users.getById(currentUser.id);
        if (userData) {
          setProfile({
            id: userData.id,
            name: userData.name || '',
            email: userData.email || '',
            role: userData.role || '',
            department: userData.department || '',
            phone: userData.phone || '',
            photoURL: userData.photoURL || '',
          });
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
      }
    };
    fetchProfile();
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 16 }}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f8f9fa' }}>
      <ScrollView style={{ width: '100%' }} contentContainerStyle={{ paddingBottom: 24, paddingTop: 16 }}>
        <View style={{ paddingHorizontal: 16 }}>

          {/* Welcome Header */}
          <View style={{ marginBottom: 20 }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#1a1a1a', marginBottom: 4 }}>
              Good Morning, {profile?.name?.split(' ')[0] || 'Manager'}!
            </Text>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{ width: 8, height: 8, borderRadius: 4, backgroundColor: '#10b981', marginRight: 6 }} />
              <Text style={{ fontSize: 12, color: '#666', marginRight: 16 }}>System Online</Text>
              <MaterialCommunityIcons name="clock-outline" size={14} color="#666" style={{ marginRight: 4 }} />
              <Text style={{ fontSize: 12, color: '#666' }}>
                {new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
              </Text>
            </View>
          </View>

          {/* Team Members */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#1e3a8a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="account-group" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.teamSize}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Team Members</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '85%', backgroundColor: '#1e3a8a', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Department Size */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#16a34a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="domain" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.departmentSize}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Department Size</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '90%', backgroundColor: '#16a34a', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Pending Approvals */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#ea580c',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="calendar-clock" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.pendingApprovals}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Pending Approvals</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Awaiting</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '25%', backgroundColor: '#ea580c', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Team Leaves */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#dc2626',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="calendar-check" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.teamLeaves}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Team Leaves</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Progress</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '40%', backgroundColor: '#dc2626', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Active Projects */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#059669',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="folder-multiple" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.teamProjects}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>Active Projects</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Progress</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '75%', backgroundColor: '#059669', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* My Announcements */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#7c3aed',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="bullhorn" size={20} color="#fff" />
                </View>
                <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#1a1a1a', marginRight: 12 }}>{stats.myAnnouncements}</Text>
                <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', flex: 1 }}>My Announcements</Text>
                <Text style={{ fontSize: 12, color: '#666', fontWeight: '500' }}>Active</Text>
              </View>
              <View style={{ height: 4, backgroundColor: '#e5e7eb', borderRadius: 2, overflow: 'hidden' }}>
                <View style={{ height: '100%', width: '60%', backgroundColor: '#7c3aed', borderRadius: 2 }} />
              </View>
            </Card.Content>
          </Card>

          {/* Team Management */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#1e3a8a',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="account-cog" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>Team Management</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>Manage Team Members and Tasks</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>

          {/* Leave Approvals */}
          <Card style={{ marginBottom: 12, borderRadius: 8, elevation: 1, backgroundColor: '#ffffff' }}>
            <Card.Content style={{ padding: 16 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <View style={{
                  width: 40,
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#ea580c',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12
                }}>
                  <MaterialCommunityIcons name="calendar-check-outline" size={20} color="#fff" />
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={{ fontSize: 14, fontWeight: '600', color: '#1a1a1a', marginBottom: 2 }}>Leave Approvals</Text>
                  <Text style={{ fontSize: 12, color: '#666' }}>Review and Approve Leave Requests</Text>
                </View>
                <MaterialCommunityIcons name="chevron-right" size={20} color="#666" />
              </View>
            </Card.Content>
          </Card>


      </View>
    </ScrollView>
  );
};

export default ManagerDashboard;
