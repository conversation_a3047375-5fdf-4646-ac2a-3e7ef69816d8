{"name": "texamworkflow", "license": "0BSD", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^6.5.17", "@react-navigation/drawer": "^6.6.3", "@react-navigation/native": "^6.1.7", "@react-navigation/native-stack": "^6.9.12", "@supabase/supabase-js": "^2.50.3", "expo": "~53.0.15", "expo-auth-session": "~6.2.0", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.2", "expo-image-picker": "~16.1.4", "expo-status-bar": "~2.2.3", "firebase": "9.23.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-get-random-values": "^1.11.0", "react-native-paper": "^5.10.4", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "react-native-gesture-handler": "~2.24.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}