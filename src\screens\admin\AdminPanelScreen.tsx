import React, { useEffect, useState } from 'react';
import { View, ScrollView, Image, Alert } from 'react-native';
import { Text, ActivityIndicator, List, TextInput, Menu, Button, Card, Portal, Dialog, RadioButton, Divider } from 'react-native-paper';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { db } from '../../services/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { DEPARTMENTS } from '../../utils/departments';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  phone?: string;
  photoURL?: string;
}

const AdminPanelScreen = ({ navigation }: any) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [deptMenuVisible, setDeptMenuVisible] = useState(false);
  const [roleMenuVisible, setRoleMenuVisible] = useState(false);
  const [currentUserRole, setCurrentUserRole] = useState<string>('employee');
  const [editDialogVisible, setEditDialogVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [newRole, setNewRole] = useState<string>('employee');
  const [newDepartment, setNewDepartment] = useState<string>('');
  const [updateLoading, setUpdateLoading] = useState(false);

  useEffect(() => {
    checkAdminAccess();
  }, []);

  const checkAdminAccess = async () => {
    const currentUser = supabaseAuthService.getCurrentUser();
    if (!currentUser) {
      navigation.goBack();
      return;
    }

    const userRole = currentUser.role || 'employee';
    setCurrentUserRole(userRole);

    if (userRole !== 'superadmin') {
      Alert.alert(
        'Access Denied',
        'Only Super Admins can access user management.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
      return;
    }

    fetchUsers();
  };

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const usersData = await db.users.getAll();
      const usersList: User[] = usersData.map((userData: any) => ({
        id: userData.id,
        name: userData.name || '',
        email: userData.email || '',
        role: userData.role || 'employee',
        department: userData.department || '',
        phone: userData.phone || '',
        photoURL: userData.photo_url || '',
      }));

      // Sort users by name
      usersList.sort((a, b) => a.name.localeCompare(b.name));
      setUsers(usersList);
    } catch (error) {
      console.error('Error fetching users:', error);
      Alert.alert('Error', 'Failed to load users. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async () => {
    if (!selectedUser) return;

    setUpdateLoading(true);
    try {
      await db.users.update(selectedUser.id, {
        role: newRole,
        department: newDepartment
      });

      // Update local state
      setUsers(users.map(user =>
        user.id === selectedUser.id
          ? { ...user, role: newRole, department: newDepartment }
          : user
      ));

      setEditDialogVisible(false);
      setSelectedUser(null);

      Alert.alert(
        'Success',
        `${selectedUser.name || selectedUser.email}'s role and department have been updated.`
      );
    } catch (error) {
      console.error('Error updating user:', error);
      Alert.alert('Error', 'Failed to update user. Please try again.');
    } finally {
      setUpdateLoading(false);
    }
  };

  const openUserEditor = (user: User) => {
    setSelectedUser(user);
    setNewRole(user.role);
    setNewDepartment(user.department || '');
    setEditDialogVisible(true);
  };

  // Use predefined departments for filter
  const departments = DEPARTMENTS.filter(dept => dept !== 'All Departments');
  
  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const searchMatch = 
      user.name.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase()) ||
      (user.department && user.department.toLowerCase().includes(search.toLowerCase()));
    
    const deptMatch = departmentFilter === 'all' || user.department === departmentFilter;
    const roleMatch = roleFilter === 'all' || user.role === roleFilter;
    
    return searchMatch && deptMatch && roleMatch;
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin': return '#dc2626';
      case 'manager': return '#f59e0b';
      case 'employee': return '#1e3a8a';
      default: return '#666';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'superadmin': return 'shield-crown';
      case 'manager': return 'account-tie';
      case 'employee': return 'account';
      default: return 'account';
    }
  };

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 16 }}>Loading users...</Text>
      </View>
    );
  }

  if (currentUserRole !== 'superadmin') {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 32 }}>
        <MaterialCommunityIcons name="shield-alert" size={64} color="#dc2626" />
        <Text style={{ fontSize: 20, fontWeight: 'bold', marginTop: 16, textAlign: 'center' }}>
          Access Denied
        </Text>
        <Text style={{ color: '#666', marginTop: 8, textAlign: 'center' }}>
          Only Super Admins can access this panel.
        </Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <View style={{ padding: 16 }}>
        {/* Header */}
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <MaterialCommunityIcons name="account-cog" size={28} color="#1e3a8a" />
          <Text style={{ fontSize: 24, fontWeight: 'bold', marginLeft: 12, color: '#1e3a8a' }}>
            Manage Users
          </Text>
        </View>

        <Text style={{ color: '#666', marginBottom: 16 }}>
          Manage user roles and departments
        </Text>
        
        {/* Search and Filters */}
        <TextInput
          placeholder="Search by name, email, or department"
          value={search}
          onChangeText={setSearch}
          style={{ marginBottom: 12 }}
          left={<TextInput.Icon icon="magnify" />}
        />
        
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 }}>
          <Menu
            visible={deptMenuVisible}
            onDismiss={() => setDeptMenuVisible(false)}
            anchor={
              <Button mode="outlined" onPress={() => setDeptMenuVisible(true)} style={{ flex: 1, marginRight: 8 }}>
                {departmentFilter === 'all' ? 'All Departments' : departmentFilter}
              </Button>
            }
          >
            <Menu.Item onPress={() => setDepartmentFilter('all')} title="All Departments" />
            {departments.map(dept => (
              <Menu.Item key={dept} onPress={() => setDepartmentFilter(dept)} title={dept} />
            ))}
          </Menu>
          
          <Menu
            visible={roleMenuVisible}
            onDismiss={() => setRoleMenuVisible(false)}
            anchor={
              <Button mode="outlined" onPress={() => setRoleMenuVisible(true)} style={{ flex: 1, marginLeft: 8 }}>
                {roleFilter === 'all' ? 'All Roles' : roleFilter.charAt(0).toUpperCase() + roleFilter.slice(1)}
              </Button>
            }
          >
            <Menu.Item onPress={() => setRoleFilter('all')} title="All Roles" />
            <Menu.Item onPress={() => setRoleFilter('employee')} title="Employee" />
            <Menu.Item onPress={() => setRoleFilter('manager')} title="Manager" />
            <Menu.Item onPress={() => setRoleFilter('superadmin')} title="Superadmin" />
          </Menu>
        </View>

        {/* Results count */}
        <Text style={{ color: '#666', marginBottom: 16 }}>
          {filteredUsers.length} {filteredUsers.length === 1 ? 'user' : 'users'} found
        </Text>
      </View>

      {/* User List */}
      <ScrollView style={{ flex: 1 }}>
        {filteredUsers.length === 0 ? (
          <View style={{ padding: 32, alignItems: 'center' }}>
            <MaterialCommunityIcons name="account-search" size={64} color="#ccc" />
            <Text style={{ color: '#666', marginTop: 16, textAlign: 'center' }}>
              No users found matching your search criteria
            </Text>
          </View>
        ) : (
          filteredUsers.map((user) => (
            <Card key={user.id} style={{ margin: 8, marginHorizontal: 16 }}>
              <List.Item
                title={user.name || user.email}
                description={`${user.email}${user.department ? ' • ' + user.department : ''}`}
                left={() => (
                  <View style={{ marginRight: 12, alignItems: 'center', justifyContent: 'center' }}>
                    {user.photoURL ? (
                      <Image
                        source={{ uri: user.photoURL }}
                        style={{ width: 48, height: 48, borderRadius: 24 }}
                      />
                    ) : (
                      <View style={{
                        width: 48,
                        height: 48,
                        borderRadius: 24,
                        backgroundColor: getRoleColor(user.role),
                        justifyContent: 'center',
                        alignItems: 'center'
                      }}>
                        <MaterialCommunityIcons 
                          name={getRoleIcon(user.role)} 
                          size={24} 
                          color="white" 
                        />
                      </View>
                    )}
                  </View>
                )}
                right={() => (
                  <View style={{ justifyContent: 'center', alignItems: 'flex-end' }}>
                    <Text style={{ 
                      color: getRoleColor(user.role), 
                      fontWeight: 'bold',
                      fontSize: 12,
                      textTransform: 'uppercase',
                      marginBottom: 4
                    }}>
                      {user.role}
                    </Text>
                    <Button
                      mode="outlined"
                      onPress={() => openUserEditor(user)}
                      style={{ borderColor: getRoleColor(user.role), minWidth: 100 }}
                    >
                      Edit User
                    </Button>
                  </View>
                )}
              />
            </Card>
          ))
        )}
      </ScrollView>

      {/* Edit User Dialog */}
      <Portal>
        <Dialog visible={editDialogVisible} onDismiss={() => setEditDialogVisible(false)} style={{ maxHeight: '80%' }}>
          <Dialog.Title>Edit User</Dialog.Title>
          <Dialog.ScrollArea style={{ maxHeight: 400, paddingHorizontal: 0 }}>
            <Dialog.Content>
              {selectedUser && (
                <>
                  <Text style={{ marginBottom: 16 }}>
                    Editing: <Text style={{ fontWeight: 'bold' }}>{selectedUser.name || selectedUser.email}</Text>
                  </Text>

                  <Text style={{ marginBottom: 12, fontWeight: 'bold' }}>Select Role:</Text>
                
                <RadioButton.Group onValueChange={setNewRole} value={newRole}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>
                    <RadioButton value="employee" />
                    <MaterialCommunityIcons name="account" size={18} color="#1e3a8a" style={{ marginLeft: 8, marginRight: 8 }} />
                    <Text style={{ fontWeight: 'bold', color: '#1e3a8a', fontSize: 14 }}>Employee</Text>
                  </View>

                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>
                    <RadioButton value="manager" />
                    <MaterialCommunityIcons name="account-tie" size={18} color="#f59e0b" style={{ marginLeft: 8, marginRight: 8 }} />
                    <Text style={{ fontWeight: 'bold', color: '#f59e0b', fontSize: 14 }}>Manager</Text>
                  </View>

                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>
                    <RadioButton value="superadmin" />
                    <MaterialCommunityIcons name="shield-crown" size={18} color="#dc2626" style={{ marginLeft: 8, marginRight: 8 }} />
                    <Text style={{ fontWeight: 'bold', color: '#dc2626', fontSize: 14 }}>Super Admin</Text>
                  </View>
                </RadioButton.Group>

                <Divider style={{ marginVertical: 12 }} />

                <Text style={{ marginBottom: 8, fontWeight: 'bold', fontSize: 14 }}>Select Department:</Text>
                <View>
                  {departments.map(dept => (
                    <View key={dept} style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
                      <RadioButton
                        value={dept}
                        status={newDepartment === dept ? 'checked' : 'unchecked'}
                        onPress={() => setNewDepartment(dept)}
                      />
                      <Text style={{ marginLeft: 8, fontSize: 14 }}>{dept}</Text>
                    </View>
                  ))}
                </View>
              </>
            )}
            </Dialog.Content>
          </Dialog.ScrollArea>
          <Dialog.Actions style={{ paddingHorizontal: 16, paddingVertical: 8 }}>
            <Button
              onPress={() => setEditDialogVisible(false)}
              style={{ marginRight: 8 }}
              compact
            >
              Cancel
            </Button>
            <Button
              onPress={handleRoleChange}
              loading={updateLoading}
              disabled={updateLoading}
              mode="contained"
              compact
              style={{ minWidth: 100 }}
            >
              Update
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

export default AdminPanelScreen;
