import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert, Image } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  TextInput,
  ActivityIndicator,
  Menu,
  TouchableRipple,
  IconButton
} from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { supabaseAuthService } from '../../services/supabaseAuth';
import { projectUpdatesService } from '../../services/projectUpdatesService';
import { Project, ProjectUpdateFormData } from '../../types/projectUpdates';

interface AddProjectUpdateScreenProps {
  navigation: any;
}

const AddProjectUpdateScreen: React.FC<AddProjectUpdateScreenProps> = ({ navigation }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [heading, setHeading] = useState('');
  const [description, setDescription] = useState('');
  const [selectedImage, setSelectedImage] = useState<{
    uri: string;
    name: string;
    type: string;
  } | null>(null);
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [projectMenuVisible, setProjectMenuVisible] = useState(false);
  
  const currentUser = supabaseAuthService.getCurrentUser();

  useEffect(() => {
    loadProjects();
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Sorry, we need camera roll permissions to upload images.'
      );
    }
  };

  const loadProjects = async () => {
    try {
      const projectsData = await projectUpdatesService.getProjects();
      setProjects(projectsData.filter(p => p.status === 'active'));
    } catch (error) {
      console.error('Error loading projects:', error);
      Alert.alert('Error', 'Failed to load projects. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedImage({
          uri: asset.uri,
          name: asset.fileName || `image_${Date.now()}.jpg`,
          type: asset.type || 'image/jpeg',
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
  };

  const validateForm = (): boolean => {
    if (!selectedProject) {
      Alert.alert('Validation Error', 'Please select a project.');
      return false;
    }
    if (!heading.trim()) {
      Alert.alert('Validation Error', 'Please enter a heading.');
      return false;
    }
    if (!description.trim()) {
      Alert.alert('Validation Error', 'Please enter a description.');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !currentUser) return;

    setSubmitting(true);
    try {
      const formData: ProjectUpdateFormData = {
        projectId: selectedProject!.id,
        heading: heading.trim(),
        description: description.trim(),
        image: selectedImage, // Now uploads to Supabase Storage!
      };

      const creator = {
        id: currentUser.id,
        name: currentUser.name || currentUser.email,
        role: currentUser.role as 'manager' | 'superadmin',
      };

      console.log('Creating project update with data:', formData);
      console.log('Creator info:', creator);

      const updateId = await projectUpdatesService.createProjectUpdate(formData, creator);
      console.log('Project update created successfully with ID:', updateId);

      Alert.alert(
        'Success',
        'Project update created successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Detailed error creating project update:', error);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      Alert.alert('Error', `Failed to create project update: ${error.message}`);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading projects...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <Card style={styles.headerCard}>
        <Card.Content>
          <View style={styles.headerContent}>
            <MaterialCommunityIcons name="clipboard-plus" size={32} color="#1e3a8a" />
            <View style={styles.headerText}>
              <Text style={styles.headerTitle}>Create Project Update</Text>
              <Text style={styles.headerSubtitle}>
                Share progress and updates with your team
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      {/* Form */}
      <Card style={styles.formCard}>
        <Card.Content>
          {/* Project Selection */}
          <Text style={styles.fieldLabel}>Project *</Text>
          <Menu
            visible={projectMenuVisible}
            onDismiss={() => setProjectMenuVisible(false)}
            anchor={
              <TouchableRipple
                onPress={() => setProjectMenuVisible(true)}
                style={styles.projectSelector}
              >
                <View style={styles.projectSelectorContent}>
                  <Text style={[
                    styles.projectSelectorText,
                    !selectedProject && styles.placeholderText
                  ]}>
                    {selectedProject ? selectedProject.name : 'Select a project'}
                  </Text>
                  <MaterialCommunityIcons 
                    name="chevron-down" 
                    size={24} 
                    color="#666" 
                  />
                </View>
              </TouchableRipple>
            }
          >
            {projects.map((project) => (
              <Menu.Item
                key={project.id}
                onPress={() => {
                  setSelectedProject(project);
                  setProjectMenuVisible(false);
                }}
                title={project.name}
                leadingIcon="folder"
              />
            ))}
          </Menu>

          {/* Heading */}
          <Text style={styles.fieldLabel}>Heading *</Text>
          <TextInput
            mode="outlined"
            value={heading}
            onChangeText={setHeading}
            placeholder="Enter update heading"
            style={styles.textInput}
            maxLength={100}
          />
          <Text style={styles.characterCount}>{heading.length}/100</Text>

          {/* Description */}
          <Text style={styles.fieldLabel}>Description *</Text>
          <TextInput
            mode="outlined"
            value={description}
            onChangeText={setDescription}
            placeholder="Describe the project update in detail"
            multiline
            numberOfLines={6}
            style={[styles.textInput, styles.descriptionInput]}
            maxLength={1000}
          />
          <Text style={styles.characterCount}>{description.length}/1000</Text>

          {/* Image Upload */}
          <Text style={styles.fieldLabel}>Image (Optional)</Text>
          {selectedImage ? (
            <View style={styles.imageContainer}>
              <Image source={{ uri: selectedImage.uri }} style={styles.selectedImage} />
              <IconButton
                icon="close-circle"
                size={24}
                iconColor="#ef4444"
                style={styles.removeImageButton}
                onPress={removeImage}
              />
            </View>
          ) : (
            <TouchableRipple onPress={pickImage} style={styles.imageUploadArea}>
              <View style={styles.imageUploadContent}>
                <MaterialCommunityIcons name="camera-plus" size={48} color="#666" />
                <Text style={styles.imageUploadText}>Tap to add image</Text>
                <Text style={styles.imageUploadSubtext}>
                  Choose from gallery
                </Text>
                <Text style={[styles.imageUploadSubtext, { fontSize: 10, color: '#4ade80', marginTop: 4 }]}>
                  ✅ Supabase Storage enabled!
                </Text>
              </View>
            </TouchableRipple>
          )}

          {/* Submit Button */}
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={submitting}
            disabled={submitting}
            style={styles.submitButton}
            icon="send"
          >
            {submitting ? 'Creating Update...' : 'Create Update'}
          </Button>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  headerCard: {
    margin: 16,
    marginBottom: 8,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerText: {
    marginLeft: 16,
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e3a8a',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  formCard: {
    margin: 16,
    marginTop: 8,
    elevation: 1,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
    marginTop: 16,
  },
  projectSelector: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    backgroundColor: '#fff',
  },
  projectSelectorContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  projectSelectorText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    color: '#999',
  },
  textInput: {
    backgroundColor: '#fff',
  },
  characterCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 4,
  },
  descriptionInput: {
    minHeight: 120,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  selectedImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  imageUploadArea: {
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
    borderRadius: 8,
    backgroundColor: '#fafafa',
    marginBottom: 16,
  },
  imageUploadContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  imageUploadText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
  },
  imageUploadSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  submitButton: {
    marginTop: 24,
    paddingVertical: 8,
  },
});

export default AddProjectUpdateScreen;
